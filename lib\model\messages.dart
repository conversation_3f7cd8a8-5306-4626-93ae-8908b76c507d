class Messages {
  String? currentDate;
  String? wrongEmailAndPassword;
  String? emailNotExist;
  String? profileUpdated;
  String? noLanguageFound, currentPassword;
  String? login, doYouWantSigOut;
  String? dashboard;
  String? myProfile;
  String? myStories;
  String? fontSize;
  String? signOut;
  String? aboutUs;
  String? joinUs;
  String? advertise;
  String? contactUs;
  String? rateUsAndroid;
  String? rateUsIos;
  String? policyAndTerms;
  String? guest;
  String? darkMode;
  String? notifications;
  String? logout;
  String? areYouSureYouWantToLogout;
  String? no;
  String? yes;
  String? submit;
  String? resetPassword;
  String? forgotPassword;
  String? signIn;
  String? signUp;
  String? adPage;
  String? showBanner;
  String? googleSignIn, appleSignIn, facebookSignIn;
  String? showBannerWithOffset;
  String? removeBanner, changePassword;
  String? information;
  String? facebookLoginNotAvailable;
  String? ok;
  String? appname;
  String? skip;
  String? updatingFeed;
  String? categoryPost;
  String? noResultsFoundMatchingWithYourKeyword;
  String? eNews;
  String? thatsAllFolks;
  String? stayBlessedAndConnected;
  String? welcome;
  String? welcomeGuest;
  String? featuredStories;
  String? filterByTopics;
  String? myFeed;
  String? latestPost;
  String? liveNews;
  String? image;
  String? toStopPlayingTapAgain;
  String? swipeTo;
  String? readFull;
  String? doYouAgree;
  String? thankYouForParticipating;
  String? mySavedStories;
  String? noSavedPostFound;
  String? searchStories, searchYourKeyword;
  String? view;
  String? eDIT;
  String? name;
  String? email;
  String? mobile, nofeedSelected;
  String? password;
  String? deleteAccount;
  String? confirmDeleteAccount;
  String? updateProfile;
  String? enterAValidEmail;
  String? reEnterPassword;
  String? newEnterPassword, newpassword;
  String? tap;
  String? enterAValidPassword;
  String? otp;
  String? enterAValidOtp;
  String? passwordShouldBeMoreThanThereeCharacter;
  String? newUser;
  String? enterAValidPhoneNumber;
  String? alreadyHaveAnAccount;
  String? userName;
  String? enterAValidUserName;
  String? phoneNumber = 'Phone number';
  String? alredayHaveAccout;
  String? noNewsAvilable;
  String? openingNewsInWeb;
  String? invalidLink;
  String? noResultFound;
  String? invalidOtpEntered;
  String? passwordAndConfirmPasswordShouldBeSame;
  String? profileUpdatedSuccessfully;
  String? shareMessage;
  String? blogFontSize;
  String? rateUs;
  String? settings;
  String? enableDisablePushNotification;
  String? enableDisableDarkMode;
  String? selectPersonalization;
  String? selectLanguage, selectLanguageSubitle;
  String? chooseYourAppLanguage;
  String? newPasswordOldPasswordNotSame;
  String? setYourBlogFontSize;
  String? saveYourInterestsForBetterContentExperience, editSaveInterests;
  String? saveInterests, oops, noCategoryPost;
  String? settingsPage;
  String? edit, editButtonInterests;
  String? noInternetConnection;
  String? chooseYourLanguage;
  String? chooseYourLanguage2, allNews;
  String? changePasswordSuccess;
  String? confirmpassword;
  String? entercurrentpassword;
  String? copiedToClipboard;
  String? oldPasswordError;
  String? confirmExitApp;
  String? confirmExitTitle, poll, great, noShortsTitle, noShortsDescription;
  String? bookmarkSave,
      bookmarkRemove,
      searchFieldEmpty,
      youHaveViewedAll,
      shorts;
  String? minimum3Select,
      otpButton,
      openBrowser,
      copyLink,
      fontDummyText,
      fontA,
      userDeleteContact,
      doYouWantToSaveChanges;
  String? otpDescription,
      resendCodeIn,
      accountDeleted,
      userNotFound,
      contactMail,
      resendCode,
      loadingOTP,
      otpSent,
      promotedAd,
      otpVerified,
      otpExpired,
      sendAgain;

  Messages({
    this.currentDate,
    this.noShortsTitle,
    this.noShortsDescription,
    this.userNotFound,
    this.promotedAd,
    this.bookmarkRemove,
    this.shorts,
    this.doYouWantToSaveChanges,
    this.accountDeleted,
    this.contactMail,
    this.fontDummyText,
    this.bookmarkSave,
    this.copiedToClipboard,
    this.searchFieldEmpty,
    this.great,
    this.userDeleteContact,
    this.youHaveViewedAll,
    this.resendCodeIn,
    this.resendCode,
    this.otpDescription,
    this.fontA,
    this.sendAgain,
    this.minimum3Select,
    this.wrongEmailAndPassword,
    this.emailNotExist,
    this.poll,
    this.editSaveInterests,
    this.profileUpdated,
    this.noLanguageFound,
    this.confirmpassword,
    this.confirmExitTitle,
    this.confirmExitApp,
    this.currentPassword,
    this.login,
    this.dashboard,
    this.myProfile,
    this.myStories,
    this.allNews,
    this.nofeedSelected,
    this.changePasswordSuccess,
    this.entercurrentpassword,
    this.fontSize,
    this.signOut,
    this.oldPasswordError,
    this.aboutUs,
    this.joinUs,
    this.otpSent,
    this.advertise,
    this.contactUs,
    this.policyAndTerms,
    this.guest,
    this.darkMode,
    this.notifications,
    this.logout,
    this.areYouSureYouWantToLogout,
    this.selectLanguageSubitle,
    this.no,
    this.yes,
    this.submit,
    this.resetPassword,
    this.changePassword,
    this.forgotPassword,
    this.signIn,
    this.signUp,
    this.adPage,
    this.showBanner,
    this.googleSignIn,
    this.appleSignIn,
    this.facebookSignIn,
    this.showBannerWithOffset,
    this.removeBanner,
    this.information,
    this.facebookLoginNotAvailable,
    this.ok,
    this.appname,
    this.skip,
    this.updatingFeed,
    this.categoryPost,
    this.noResultsFoundMatchingWithYourKeyword,
    this.eNews,
    this.thatsAllFolks,
    this.stayBlessedAndConnected,
    this.welcome,
    this.welcomeGuest,
    this.featuredStories,
    this.filterByTopics,
    this.myFeed,
    this.latestPost,
    this.liveNews,
    this.noCategoryPost,
    this.image,
    this.toStopPlayingTapAgain,
    this.swipeTo,
    this.readFull,
    this.rateUsAndroid,
    this.rateUsIos,
    this.doYouAgree,
    this.thankYouForParticipating,
    this.mySavedStories,
    this.noSavedPostFound,
    this.searchStories,
    this.view,
    this.eDIT,
    this.name,
    this.email,
    this.mobile,
    this.otpExpired,
    this.otpVerified,
    this.password,
    this.deleteAccount,
    this.confirmDeleteAccount,
    this.updateProfile,
    this.enterAValidEmail,
    this.reEnterPassword,
    this.newEnterPassword,
    this.tap,
    this.enterAValidPassword,
    this.otp,
    this.enterAValidOtp,
    this.passwordShouldBeMoreThanThereeCharacter,
    this.newUser,
    this.enterAValidPhoneNumber,
    this.alreadyHaveAnAccount,
    this.userName,
    this.enterAValidUserName,
    this.phoneNumber,
    this.alredayHaveAccout,
    this.noNewsAvilable,
    this.openingNewsInWeb,
    this.invalidLink,
    this.noResultFound,
    this.invalidOtpEntered,
    this.shareMessage,
    this.passwordAndConfirmPasswordShouldBeSame,
    this.profileUpdatedSuccessfully,
    this.settings,
    this.selectPersonalization,
    this.selectLanguage,
    this.saveInterests,
    this.settingsPage,
    this.otpButton,
    this.edit,
    this.newPasswordOldPasswordNotSame,
    this.chooseYourAppLanguage,
    this.enableDisableDarkMode,
    this.enableDisablePushNotification,
    this.rateUs,
    this.saveYourInterestsForBetterContentExperience,
    this.setYourBlogFontSize,
    this.blogFontSize,
    this.openBrowser,
    this.copyLink,
    this.chooseYourLanguage,
    this.chooseYourLanguage2,
    this.noInternetConnection,
    this.oops,
  });

  Messages.fromJson(Map<String, dynamic> json) {
    currentDate = json['app_current_date'];
    wrongEmailAndPassword = json['app_wrong_email_and_password'];
    emailNotExist = json['app_email_not_exist'];
    profileUpdated = json['app_profile_updated'];
    noLanguageFound = json['app_no_language_found'];
    confirmExitApp = json['confirm_exit_app_desc'];
    otpSent = json['otp_sent'];
    confirmExitTitle = json['confirm_exit_title'];
    login = json['app_login'];
    dashboard = json['app_dashboard'];
    myProfile = json['app_my_profile'];
    myStories = json['app_my_stories'];
    fontSize = json['app_font_size'];
    signOut = json['app_sign_out'];
    aboutUs = json['app_about_us'];
    nofeedSelected = json['no_feed_selected'];
    noCategoryPost = json['no_category_post'];
    joinUs = json['app_join_us'];

    userNotFound = json['user_not_found'];
    searchFieldEmpty = json['search_field_empty'];
    advertise = json['app_advertise'];
    fontDummyText = json['font_dummy_text'];
    copiedToClipboard = json['copied_to_clipboard'];
    allNews = json['app_all_news'];
    userDeleteContact = json['user_delete_contact'];
    poll = json['poll'];
    oldPasswordError = json['old_password_error'];
    bookmarkRemove = json['bookmark_remove'];
    bookmarkSave = json['bookmark_save'];
    fontA = json['font_a'];
    contactUs = json['app_contact_us'];
    policyAndTerms = json['app_policy_and_terms'];
    guest = json['app_guest'];
    darkMode = json['app_dark_mode'];
    newPasswordOldPasswordNotSame = json['new_password_old_password_not_same'];
    rateUsIos = json['rate_us_ios'];
    rateUsAndroid = json['rate_us_android'];
    notifications = json['app_notifications'];
    openBrowser = json['open_browser'];
    logout = json['app_logout'];
    areYouSureYouWantToLogout = json['app_are_you_sure_you_want_to_logout'];
    no = json['app_no'];
    selectLanguageSubitle = json['app_select_language_subitle'];
    yes = json['app_yes'];
    resendCode = json['resend_code'];
    doYouWantToSaveChanges = json['do_you_want_to_save_changes'];
    resendCodeIn = json['resent_code_in'];
    otpDescription = json['otp_description'];
    submit = json['app_submit'];
    resetPassword = json['app_reset_password'];
    great = json['great'];
    shorts = json['shorts'];
    forgotPassword = json['app_forgot_password'];
    signIn = json['app_sign_in'];
    signUp = json['app_sign_up'];
    adPage = json['app_ad_page'];
    searchYourKeyword = json['search_your_keyword'];
    sendAgain = json['send_again'];
    showBanner = json['app_show_banner'];
    showBannerWithOffset = json['app_show_banner_with_offset'];
    contactMail = json['contact_mail'];
    removeBanner = json['app_remove_banner'];
    information = json['app_information'];
    facebookLoginNotAvailable = json['app_facebook_login_not_available'];
    ok = json['app_ok'];
    doYouWantSigOut = json['do_you_want_signout'];
    appname = json['app_appname'];
    skip = json['app_skip'];
    updatingFeed = json['app_updating_feed'];
    changePasswordSuccess = json['change_password_success'];
    categoryPost = json['app_category_post'];
    noResultsFoundMatchingWithYourKeyword =
        json['app_no_results_found_matching_with_your_keyword'];
    eNews = json['app_e_news'];
    thatsAllFolks = json['app_thats_all_folks'];
    stayBlessedAndConnected = json['app_stay_blessed_and_Connected'];
    welcome = json['app_welcome'];
    welcomeGuest = json['app_welcome_guest'];
    youHaveViewedAll = json['you_have_viewed_all'];
    featuredStories = json['app_featured_stories'];
    filterByTopics = json['Filter by topic'];
    myFeed = json['app_my_feed'];
    latestPost = json['app_latest_post'];
    liveNews = json['app_live_news'];
    image = json['app_image'];
    toStopPlayingTapAgain = json['app_to_stop_playing_tap_again'];
    swipeTo = json['app_swipe_to'];
    readFull = json['app_read_full'];
    minimum3Select = json['select_3_minimum_interests'];
    doYouAgree = json['app_do_you_agree'];
    thankYouForParticipating = json['app_thank_you_for_participating'];
    mySavedStories = json['app_my_saved_stories'];
    noSavedPostFound = json['app_no_saved_post_found'];
    searchStories = json['app_search_stories'];
    view = json['app_view'];
    promotedAd = json['promoted_ad'];

    name = json['app_name'];
    email = json['app_email'];
    confirmpassword = json['app_confirm_password'];
    mobile = json['app_mobile'];
    otpButton = json['verify_otp_button'];
    password = json['app_password'];
    deleteAccount = json['app_delete_account'];
    confirmDeleteAccount = json['app_confirm_delete_account'];
    updateProfile = json['app_update_profile'];
    enterAValidEmail = json['app_enter_a_valid_email'];
    reEnterPassword = json['app_re_enter_password'];
    newEnterPassword = json['app_new_enter_password'];
    tap = json['app_tap_me'];
    enterAValidPassword = json['app_enter_a_valid_password'];
    otp = json['app_otp'];
    enterAValidOtp = json['app_enter_a_valid_otp'];
    loadingOTP = json['preparing_otp'];
    passwordShouldBeMoreThanThereeCharacter =
        json['app_password_should_be_more_than_theree_character'];
    newUser = json['app_new_user'];
    enterAValidPhoneNumber = json['app_enter_a_valid_phone_number'];
    alreadyHaveAnAccount = json['app_already_have_an_account'];
    userName = json['app_user_name'];
    enterAValidUserName = json['app_enter_a_valid_user_name'];
    phoneNumber = json['app_phone_number'];
    alredayHaveAccout = json['app_alreday_have_accout'];
    noNewsAvilable = json['app_no_news_available'];
    openingNewsInWeb = json['app_opening_news_in_web'];
    invalidLink = json['app_invalid_link'];
    noResultFound = json['app_no_result_found'];
    otpExpired = json['otp_expired'];
    otpVerified = json['otp_verified'];
    invalidOtpEntered = json['app_invalid_otp_entered'];
    passwordAndConfirmPasswordShouldBeSame =
        json['app_password_and_confirm_password_should_be_same'];
    profileUpdatedSuccessfully = json['app_profile_updated_successfully'];
    shareMessage = json['app_share_message'];
    rateUs = json['app_rate_us'];
    settings = json['app_settings'];
    enableDisablePushNotification =
        json['app_enable_disable_push_notification'];
    enableDisableDarkMode = json['app_enable_disable_dark_mode'];
    selectPersonalization = json['app_select_personalization'];
    copyLink = json['copy_link'];
    selectLanguage = json['app_select_language'];
    chooseYourAppLanguage = json['app_choose_your_app_language'];
    setYourBlogFontSize = json['app_set_your_blog_font_size'];
    saveYourInterestsForBetterContentExperience =
        json['app_save_your_interests_for_better_content_experience'];
    saveInterests = json['app_save_interests'];
    settingsPage = json['app_settings_page'];
    edit = json['app_edit'];
    blogFontSize = json['app_blog_font_size'];
    noInternetConnection = json['app_no_internet_connection'];
    chooseYourLanguage = json['app_choose_your_language'];
    chooseYourLanguage2 = json['app_choose_your_language_2'];

    // ------ new addition ------------
    googleSignIn = json['app_google_signin'];
    appleSignIn = json['app_apple_signin'];
    facebookSignIn = json['app_fb_signin'];
    oops = json['oops'];
    newpassword = json['app_new_password'];
    currentPassword = json['app_current_password'];
    entercurrentpassword = json['app_enter_current_password'];
    editButtonInterests = json['app_edit_button_interests'];
    confirmpassword = json['app_confirm_password'];
    editSaveInterests = json['app_edit_save_interests'];
    changePassword = json['app_change_password'];

    noShortsTitle = json['no_shorts_title'];
    noShortsDescription = json['no_shorts_description'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['app_current_date'] = currentDate;
    data['app_wrong_email_and_password'] = wrongEmailAndPassword;
    data['app_email_not_exist'] = emailNotExist;
    data['no_feed_selected'] = nofeedSelected;
    data['oops'] = oops;
    data['do_you_want_signout'] = doYouWantSigOut;
    data['app_profile_updated'] = profileUpdated;
    data['you_have_viewed_all'] = youHaveViewedAll;
    data['bookmark_save'] = bookmarkSave;
    data['bookmark_remove'] = bookmarkRemove;
    data['otp_sent'] = otpSent;
    data['otp_expired'] = otpExpired;
    data['otp_verified'] = otpVerified;
    data['app_edit_button_interests'] = editButtonInterests;
    data['app_no_language_found'] = noLanguageFound;
    data['user_not_found'] = userNotFound;
    data['app_login'] = login;
    data['app_new_password'] = newpassword;
    data['font_dummy_text'] = fontDummyText;
    data['shorts'] = shorts;
    data['rate_us_ios'] = rateUsIos;
    data['search_your_keyword'] = searchYourKeyword;
    data['rate_us_android'] = rateUsAndroid;
    data['user_delete_contact'] = userDeleteContact;
    data['new_password_old_password_not_same'] = newPasswordOldPasswordNotSame;
    data['app_edit_save_interests'] = editSaveInterests;
    data['contact_mail'] = contactMail;
    data['preparing_otp'] = loadingOTP;
    data['app_confirm_password'] = confirmpassword;
    data['app_google_signin'] = googleSignIn;
    data['app_apple_signin'] = appleSignIn;
    data['app_change_password'] = changePassword;
    data['app_fb_signin'] = facebookSignIn;
    data['send_again'] = sendAgain;
    data['font_a'] = fontA;
    data['app_dashboard'] = dashboard;
    data['confirm_exit_app_desc'] = confirmExitApp;
    data['confirm_exit_title'] = confirmExitTitle;
    data['app_my_profile'] = myProfile;
    data['search_field_empty'] = searchFieldEmpty;
    data['app_my_stories'] = myStories;
    data['app_font_size'] = fontSize;
    data['resend_code'] = resendCode;
    data['resent_code_in'] = resendCodeIn;
    data['otp_description'] = otpDescription;
    data['app_sign_out'] = signOut;
    data['select_3_minimum_interests'] = minimum3Select;
    data['app_about_us'] = aboutUs;
    data['app_join_us'] = joinUs;
    data['app_advertise'] = advertise;
    data['change_password_success'] = changePasswordSuccess;
    data['app_contact_us'] = contactUs;
    data['app_confirm_password'] = confirmpassword;
    data['do_you_want_to_save_changes'] = doYouWantToSaveChanges;
    data['app_policy_and_terms'] = policyAndTerms;
    data['app_guest'] = guest;
    data['app_dark_mode'] = darkMode;
    data['app_notifications'] = notifications;
    data['app_logout'] = logout;
    data['app_are_you_sure_you_want_to_logout'] = areYouSureYouWantToLogout;
    data['app_no'] = no;
    data['copied_to_clipboard'] = copiedToClipboard;
    data['copy_link'] = copyLink;
    data['app_yes'] = yes;
    data['app_submit'] = submit;
    data['no_category_post'] = noCategoryPost;
    data['old_password_error'] = oldPasswordError;
    data['app_select_language_subitle'] = selectLanguageSubitle;
    data['app_reset_password'] = resetPassword;
    data['app_forgot_password'] = forgotPassword;
    data['app_sign_in'] = signIn;
    data['app_sign_up'] = signUp;
    data['app_ad_page'] = adPage;
    data['app_show_banner'] = showBanner;
    data['app_show_banner_with_offset'] = showBannerWithOffset;
    data['app_remove_banner'] = removeBanner;
    data['app_information'] = information;
    data['great'] = great;
    data['app_facebook_login_not_available'] = facebookLoginNotAvailable;
    data['app_ok'] = ok;
    data['app_appname'] = appname;
    data['app_skip'] = skip;
    data['app_updating_feed'] = updatingFeed;
    data['app_category_post'] = categoryPost;
    data['app_no_results_found_matching_with_your_keyword'] =
        noResultsFoundMatchingWithYourKeyword;
    data['app_e_news'] = eNews;
    data['app_thats_all_folks'] = thatsAllFolks;
    data['app_stay_blessed_and_Connected'] = stayBlessedAndConnected;
    data['app_welcome'] = welcome;
    data['app_welcome_guest'] = welcomeGuest;
    data['app_featured_stories'] = featuredStories;
    data['Filter by topic'] = filterByTopics;
    data['app_my_feed'] = myFeed;
    data['app_latest_post'] = latestPost;
    data['app_live_news'] = liveNews;
    data['app_image'] = image;
    data['app_to_stop_playing_tap_again'] = toStopPlayingTapAgain;
    data['app_swipe_to'] = swipeTo;
    data['app_read_full'] = readFull;
    data['app_do_you_agree'] = doYouAgree;
    data['app_thank_you_for_participating'] = thankYouForParticipating;
    data['app_my_saved_stories'] = mySavedStories;
    data['app_no_saved_post_found'] = noSavedPostFound;
    data['app_search_stories'] = searchStories;
    data['app_view'] = view;
    data['app_eDIT'] = eDIT;
    data['app_name'] = name;
    data['app_email'] = email;
    data['app_mobile'] = mobile;
    data['app_password'] = password;
    data['app_delete_account'] = deleteAccount;
    data['app_confirm_delete_account'] = confirmDeleteAccount;
    data['app_update_profile'] = updateProfile;
    data['app_enter_a_valid_email'] = enterAValidEmail;
    data['app_re_enter_password'] = reEnterPassword;
    data['app_new_enter_password'] = newEnterPassword;
    data['app_tap_me'] = tap;
    data['app_enter_a_valid_password'] = enterAValidPassword;
    data['app_otp'] = otp;
    data['app_enter_a_valid_otp'] = enterAValidOtp;
    data['verify_otp_button'] = otpButton;
    data['app_password_should_be_more_than_theree_character'] =
        passwordShouldBeMoreThanThereeCharacter;
    data['app_new_user'] = newUser;
    data['app_enter_a_valid_phone_number'] = enterAValidPhoneNumber;
    data['app_already_have_an_account'] = alreadyHaveAnAccount;
    data['app_user_name'] = userName;
    data['app_enter_a_valid_user_name'] = enterAValidUserName;
    data['app_phone_number'] = phoneNumber;
    data['app_alreday_have_accout'] = alredayHaveAccout;
    data['app_no_news_available'] = noNewsAvilable;
    data['app_opening_news_in_web'] = openingNewsInWeb;
    data['app_invalid_link'] = invalidLink;
    data['app_no_result_found'] = noResultFound;
    data['select_3_minimum_interests'] = minimum3Select;
    data['promoted_ad'] =  promotedAd ;
    data['app_invalid_otp_entered'] = invalidOtpEntered;
    data['app_password_and_confirm_password_should_be_same'] =
        passwordAndConfirmPasswordShouldBeSame;
    data['app_profile_updated_successfully'] = profileUpdatedSuccessfully;
    data['app_share_message'] = shareMessage;
    data['app_rate_us'] = rateUs;
    data['app_settings'] = settings;
    data['app_enable_disable_push_notification'] =
        enableDisablePushNotification;
    data['app_enable_disable_dark_mode'] = enableDisableDarkMode;
    data['app_select_personalization'] = selectPersonalization;
    data['app_select_language'] = selectLanguage;
    data['app_choose_your_app_language'] = chooseYourAppLanguage;
    data['app_set_your_blog_font_size'] = setYourBlogFontSize;
    data['app_save_your_interests_for_better_content_experience'] =
        saveYourInterestsForBetterContentExperience;
    data['app_save_interests'] = saveInterests;
    data['app_settings_page'] = settingsPage;
    data['app_edit'] = edit;
    data['app_blog_font_size'] = blogFontSize;
    data['app_no_internet_connection'] = noInternetConnection;
    data['app_choose_your_language'] = chooseYourLanguage;
    data['app_choose_your_language2'] = chooseYourLanguage2;
    data['no_shorts_title'] = noShortsTitle;
    data['no_shorts_description'] = noShortsDescription;
    return data;
  }
}

// Map<String, dynamic> jsonData(){
//    return
// }



class ReceivedNotification {
  int? id;
  String? title;
  String? body, payload;

  ReceivedNotification(
      {this.id,
      this.title,
      this.payload,
      this.body
    });

  ReceivedNotification.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    title = json['title'];
    payload = json['payload'];
    body = json['body'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['title'] = title;
    data['body'] = body;
    data['payload'] = payload;
    return data;
  }
}