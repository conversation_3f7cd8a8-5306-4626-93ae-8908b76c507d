import 'dart:async';
import 'dart:io';
import 'dart:ui' as ui;
import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:incite/api_controller/user_controller.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';

class ShareCard extends StatelessWidget {
  final String title;
  final String content;
  final GlobalKey previewContainer;

  const ShareCard({
    super.key,
    required this.title,
    required this.content,
    required this.previewContainer,
  });

  @override
  Widget build(BuildContext context) {
    return RepaintBoundary(
      key: previewContainer,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Image.asset(
                  'assets/images/com_logo.png',
                  height: 40,
                  width: 40,
                ),
                const SizedBox(width: 12),
                const Text(
                  'quicknews',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              title,
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              content,
              style: const TextStyle(
                fontSize: 16,
                height: 1.5,
              ),
            ),
          ],
        ),
      ),
    );
  }
}



Future<Uint8List?> captureScreenshot(GlobalKey previewContainer,{isPost =false,isQuote=false})async {
  try {
    RenderRepaintBoundary? boundary =
    previewContainer.currentContext!.findRenderObject() as RenderRepaintBoundary;
    ui.Image image = await boundary.toImage(pixelRatio: 3.0);
    ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);

    if (byteData == null) return null;

    Uint8List screenshotBytes = byteData.buffer.asUint8List();

    // Load only the App Store icon (Removed Play Store Icon)
    ByteData appStoreIconData = await rootBundle.load('assets/images/com_logo.png');

    // Decode App Store icon
    ui.Codec appStoreIconCodec = await ui.instantiateImageCodec(
      appStoreIconData.buffer.asUint8List(),
      targetHeight: 180,
      targetWidth: 180,
    );

    // Convert to frame and image
    ui.FrameInfo appStoreIconFrame = await appStoreIconCodec.getNextFrame();
    ui.Image appStoreIconImage = appStoreIconFrame.image;

    int width = image.width;
    int height = image.height;
    ui.PictureRecorder recorder = ui.PictureRecorder();
    ui.Canvas canvas = ui.Canvas(recorder);
    canvas.drawImage(image, Offset.zero, Paint());

    // Positioning for App Store icon (Play Store icon is removed)
    double xPos = width / 1.65;
    double yPos = height / 1.15 - 20 - 28.0;
    canvas.drawImage(appStoreIconImage, Offset(xPos, yPos), Paint());

    // Convert final image to bytes
    ui.Picture finalByteData = recorder.endRecording();
    ui.Image finalImage = await finalByteData.toImage(width, height);
    ByteData? finalByte = await finalImage.toByteData(format: ImageByteFormat.png);

    if (finalByte == null) return null;

    Uint8List finalBytes = finalByte.buffer.asUint8List();
    return isPost ? screenshotBytes : finalBytes;
  } catch (e) {
    debugPrint(e.toString());
    return null;
  }
}

void shareText(String link) async {
  try {
    await Share.share(
        "${allMessages.value.shareMessage}\n$link"
    );
  } catch (e) {
    debugPrint('Error sharing screenshot: $e');
  }
}


void shareImage(XFile data,String link) async {
  try {
    await Share.shareXFiles(
      [data],
      subject: 'screenshot.png',
      text: allSettings.value.enableShareSetting == '1'
          ? "${allMessages.value.shareMessage}\n$link"
          : "${allMessages.value.shareMessage}",
    );
  } catch (e) {
    debugPrint('Error sharing screenshot: $e');
  }
}



Future<XFile> convertToXFile(Uint8List data, {String fileName = 'image.png'}) async {

  // Get the temporary directory path
  Directory tempDir = await getTemporaryDirectory();

  String tempPath = tempDir.path;
  // Create a temporary file path
  String filePath = path.join(tempPath, fileName);
  // Save the Uint8List data as a file
  await File(filePath).writeAsBytes(data);
  // Create an XFile from the temporary file path
  XFile xFile = XFile(filePath);

  return xFile;
}