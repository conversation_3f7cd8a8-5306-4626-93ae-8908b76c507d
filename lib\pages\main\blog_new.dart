import 'dart:io';
import 'dart:typed_data';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:html/parser.dart';
import 'package:incite/model/blog.dart';
import 'package:incite/api_controller/app_provider.dart';
import 'package:incite/utils/color_util.dart';
import 'package:incite/utils/image_util.dart';
import 'package:incite/utils/screen_util.dart';
import 'package:incite/utils/theme_util.dart';
import 'package:incite/utils/time_util.dart';
import 'package:incite/utils/rgbo_to_hex.dart';
import 'package:incite/utils/nav_util.dart';
import 'package:incite/widgets/custom_toast.dart';
import 'package:incite/widgets/shimmer.dart';
import 'package:incite/pages/main/widgets/text.dart';
import 'package:incite/pages/main/widgets/poll.dart';
import 'package:incite/pages/main/widgets/caurosal.dart';
import 'package:incite/widgets/incite_video_player.dart';
import 'package:incite/pages/main/widgets/fullscreen.dart';
import 'package:incite/pages/main/web_view.dart';
import 'package:incite/pages/main/home.dart'; // For CategoryWrap
import 'package:incite/main.dart';
import 'package:incite/api_controller/user_controller.dart'; // For currentUser
import 'package:provider/provider.dart';

class BlogPageNew extends StatefulWidget {
  final Blog? model;
  final int index;
  final int currIndex;
  final bool isBackAllowed;
  final bool initial;
  final String? type;
  final Function(bool)? onChanged;
  final VoidCallback? onTap;

  const BlogPageNew({
    Key? key,
    this.model,
    required this.index,
    required this.currIndex,
    this.isBackAllowed = false,
    this.initial = false,
    this.type,
    this.onChanged,
    this.onTap,
  }) : super(key: key);

  @override
  State<BlogPageNew> createState() => _BlogPageNewState();
}

class _BlogPageNewState extends State<BlogPageNew> {
  late Blog blog;
  AppProvider? provider;
  bool isShare = false;
  bool isExpand = false;
  GlobalKey previewContainer = GlobalKey();

  @override
  void initState() {
    super.initState();
    blog = widget.model!;
    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      provider = Provider.of<AppProvider>(context, listen: false);
      if (widget.index == 0) {
        provider!.addviewData(blog.id ?? 0);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle(
      statusBarColor: dark(context) ? ColorUtil.blogBackColor : Colors.white,
    ));

    var timeFormat2 = blog.scheduleDate == null
        ? ''
        : timeFormat(DateTime.tryParse(blog.scheduleDate!.toIso8601String()));

    return Scaffold(
      body: blog.title == null && blog.description == null
          ? _buildLoadingState()
          : SafeArea(
              child: RepaintBoundary(
                key: previewContainer,
                child: Container(
                  color: Theme.of(context).scaffoldBackgroundColor,
                  child: Column(
                    children: [
                      // Main content area
                      Expanded(
                        child: SingleChildScrollView(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // 1. Feature Image Section
                              _buildFeatureImageSection(),
                              
                              // 2. Headline/Title Section
                              _buildHeadlineSection(),
                              
                              // 5. Meta Info Row (Time & Page indicator)
                              _buildMetaInfoSection(timeFormat2),
                              
                              // 3. Article Body/Summary Section
                              _buildArticleBodySection(),
                              
                              // Ad space if needed
                              _buildAdSection(),
                              
                              // Bottom padding for footer
                              const SizedBox(height: 80),
                            ],
                          ),
                        ),
                      ),
                      
                      // 4. Footer/Engagement Bar (Fixed at bottom)
                      _buildFooterEngagementBar(),
                    ],
                  ),
                ),
              ),
            ),
    );
  }

  // Loading state with shimmer
  Widget _buildLoadingState() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          ShimmerLoader(
            width: size(context).width,
            height: 250,
            margin: const EdgeInsets.only(top: 30),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              const ShimmerLoader(
                width: 100,
                height: 20,
                borderRadius: 16,
              ),
              const Spacer(),
              ...List.generate(
                3,
                (index) => const ShimmerLoader(
                  width: 36,
                  height: 36,
                  borderRadius: 100,
                ),
              )
            ],
          ),
          const SizedBox(height: 12),
          ShimmerLoader(
            width: size(context).width,
            height: size(context).height / 2.15,
            borderRadius: 16,
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              ...List.generate(
                2,
                (index) => const ShimmerLoader(
                  width: 120,
                  height: 20,
                  borderRadius: 10,
                ),
              )
            ],
          )
        ],
      ),
    );
  }

  // 1. Feature Image Section - Large Banner Image at the top (full width)
  Widget _buildFeatureImageSection() {
    return Container(
      width: double.infinity,
      height: 250,
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12), // Slightly rounded corners
        color: Theme.of(context).cardColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Stack(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: _buildImageContent(),
          ),
          // Source watermark overlay (small source tag)
          if (blog.sourceName != null && blog.sourceName!.isNotEmpty)
            Positioned(
              bottom: 8,
              right: 8,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.7),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Text(
                  blog.sourceName!,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
          // Category tag overlay
          Positioned(
            top: 12,
            left: 12,
            child: _buildCategoryTag(),
          ),
        ],
      ),
    );
  }

  Widget _buildImageContent() {
    if (blog.videoUrl != null && blog.videoUrl!.isNotEmpty) {
      return PlayAnyVideoPlayer(
        model: blog,
        isCurrentlyOpened: widget.currIndex == widget.index,
        isPlayCenter: true,
      );
    } else if (blog.images != null && blog.images!.length > 1) {
      return CaurosalSlider(model: blog);
    } else {
      return GestureDetector(
        onTap: () {
          Navigator.push(
            context,
            PagingTransform(
              widget: FullScreen(
                image: blog.images != null && blog.images!.isNotEmpty
                    ? blog.images![0].toString()
                    : '',
                index: widget.index,
                title: blog.title.toString(),
              ),
              slideUp: true,
            ),
          );
        },
        child: blog.images == null || blog.images!.isEmpty
            ? Image.asset(Img.logo, fit: BoxFit.cover)
            : CachedNetworkImage(
                imageUrl: blog.images![0],
                fit: BoxFit.cover,
                width: double.infinity,
                height: double.infinity,
                errorWidget: (context, url, error) => const ShimmerLoader(),
                placeholder: (context, url) => const ShimmerLoader(),
              ),
      );
    }
  }

  Widget _buildCategoryTag() {
    if (blog.blogSubCategory != null && blog.blogSubCategory!.isNotEmpty) {
      return CategoryWrap(
        color: blog.blogSubCategory![0].color != null
            ? hexToRgb(blog.blogSubCategory![0].color.toString())
            : Colors.black,
        name: blog.blogSubCategory![0].name.toString(),
      );
    } else {
      return CategoryWrap(
        color: blog.categoryColor != null
            ? hexToRgb(blog.categoryColor.toString())
            : Colors.black,
        name: blog.categoryName.toString(),
      );
    }
  }

  // 2. Headline/Title Section - Bold and large font, center-aligned
  Widget _buildHeadlineSection() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
      child: TitleWidget(
        key: Key('${blog.hashCode}'),
        title: blog.title.toString(),
        size: 24, // Large font size for high visibility
        color: Theme.of(context).textTheme.headlineLarge?.color,
      ),
    );
  }

  // 5. Meta Info Row - Time posted and page indicator (small, faded font)
  Widget _buildMetaInfoSection(String timeFormat2) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Time and source info
          Expanded(
            child: _buildTimeAndSourceInfo(timeFormat2),
          ),
          // Page indicator
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              '${widget.index + 1} of 50', // Page indicator
              style: TextStyle(
                fontSize: 11,
                color: Theme.of(context).textTheme.bodySmall?.color?.withOpacity(0.7),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTimeAndSourceInfo(String timeFormat2) {
    return RichText(
      text: TextSpan(
        children: [
          if (blog.sourceLink != null && blog.sourceLink!.isNotEmpty)
            WidgetSpan(
              alignment: PlaceholderAlignment.middle,
              child: GestureDetector(
                onTap: () {
                  Navigator.push(
                    context,
                    CupertinoPageRoute(
                      builder: (context) => CustomWebView(url: blog.sourceLink.toString()),
                    ),
                  );
                },
                child: Container(
                  decoration: BoxDecoration(
                    border: Border(
                      bottom: BorderSide(
                        width: 1,
                        color: Theme.of(context).primaryColor,
                      ),
                    ),
                  ),
                  child: Text(
                    blog.sourceName ?? 'Source',
                    style: TextStyle(
                      color: Theme.of(context).primaryColor,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
            ),
          if (blog.sourceLink != null && blog.sourceLink!.isNotEmpty && timeFormat2.isNotEmpty)
            const TextSpan(text: ' • '),
          if (timeFormat2.isNotEmpty)
            TextSpan(
              text: timeFormat2,
              style: TextStyle(
                color: Theme.of(context).textTheme.bodySmall?.color?.withOpacity(0.7),
                fontSize: 12,
              ),
            ),
        ],
      ),
    );
  }

  // 3. Article Body/Summary Section - Clean text with scrolling
  Widget _buildArticleBodySection() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Description(
            optionLength: blog.question == null
                ? 0
                : blog.question!.options != null
                    ? blog.question!.options!.length
                    : 0,
            model: blog,
            isPoll: isExpand && blog.question != null && blog.isVotingEnable == 1,
          ),
          if (blog.question != null && blog.isVotingEnable == 1) ...[
            const SizedBox(height: 16),
            BlogPoll(
              pollKey: previewContainer,
              onChanged: (value) {
                isExpand = value;
                setState(() {});
              },
              model: blog,
            ),
          ],
        ],
      ),
    );
  }

  // Ad Section
  Widget _buildAdSection() {
    return const SizedBox(); // Placeholder for ads
  }

  // 4. Footer/Engagement Bar - Fixed at bottom with like, comment, share
  Widget _buildFooterEngagementBar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        border: Border(
          top: BorderSide(color: Colors.grey.shade300, width: 0.5),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          // Like button
          _buildEngagementButton(
            icon: provider != null && provider!.likedIds.contains(blog.id)
                ? Icons.favorite
                : Icons.favorite_border,
            label: 'Like',
            count: blog.isVote ?? 0,
            color: provider != null && provider!.likedIds.contains(blog.id)
                ? Colors.red
                : null,
            onTap: _handleLike,
          ),

          // Comment button
          _buildEngagementButton(
            icon: Icons.comment_outlined,
            label: 'Comment',
            count: 0, // Add comment count when available
            onTap: () {
              showCustomToast(context, 'Comments coming soon!');
            },
          ),

          // Bookmark button
          _buildEngagementButton(
            icon: provider != null && provider!.bookmarkIds.contains(blog.id)
                ? Icons.bookmark
                : Icons.bookmark_border,
            label: 'Save',
            color: provider != null && provider!.bookmarkIds.contains(blog.id)
                ? Theme.of(context).primaryColor
                : null,
            onTap: _handleBookmark,
          ),

          // Share button (right-aligned with arrow)
          _buildEngagementButton(
            icon: Icons.share_outlined,
            label: 'Share',
            onTap: _handleShare,
            isShare: true,
          ),
        ],
      ),
    );
  }

  // Helper method to build engagement buttons
  Widget _buildEngagementButton({
    required IconData icon,
    required String label,
    int? count,
    Color? color,
    required VoidCallback onTap,
    bool isShare = false,
  }) {
    return Expanded(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(25),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 10),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Stack(
                children: [
                  Icon(
                    icon,
                    color: color ?? Theme.of(context).iconTheme.color?.withOpacity(0.7),
                    size: 24,
                  ),
                  if (isShare)
                    Positioned(
                      right: -2,
                      top: -2,
                      child: Container(
                        width: 8,
                        height: 8,
                        decoration: BoxDecoration(
                          color: Theme.of(context).primaryColor,
                          shape: BoxShape.circle,
                        ),
                      ),
                    ),
                ],
              ),
              const SizedBox(height: 4),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    label,
                    style: TextStyle(
                      fontWeight: FontWeight.w500,
                      fontSize: 12,
                      color: color ?? Theme.of(context).textTheme.bodyMedium?.color?.withOpacity(0.8),
                    ),
                  ),
                  if (count != null && count > 0) ...[
                    const SizedBox(width: 4),
                    Text(
                      count.toString(),
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 12,
                        color: color ?? Theme.of(context).primaryColor,
                      ),
                    ),
                  ],
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Handle like action
  void _handleLike() {
    if (currentUser.value.id != null) {
      provider?.setLike(blog: blog);
      setState(() {});
    } else {
      Navigator.pushNamed(context, '/LoginPage');
    }
  }

  // Handle bookmark action
  void _handleBookmark() {
    if (currentUser.value.id != null) {
      if (provider != null && provider!.bookmarkIds.contains(blog.id)) {
        showCustomToast(context, 'Bookmark Removed');
        provider!.removeBookmarkData(blog.id!.toInt());
      } else {
        showCustomToast(context, 'Bookmark Saved');
        provider!.addBookmarkData(blog.id!.toInt());
      }
      provider?.setBookmark(blog: blog);
      setState(() {});
    } else {
      Navigator.pushNamed(context, '/LoginPage');
    }
  }

  // Handle share action
  void _handleShare() {
    showCustomToast(context, 'Share functionality coming soon!');
    // TODO: Implement share functionality
  }
}
