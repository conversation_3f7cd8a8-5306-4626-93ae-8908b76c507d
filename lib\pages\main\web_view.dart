
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:incite/widgets/custom_toast.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../api_controller/user_controller.dart';

class CustomWebView extends StatefulWidget {
  final String url;
  final VoidCallback? onTap;
  const CustomWebView({super.key, required this.url, this.onTap});
  @override
  State<CustomWebView> createState() => _CustomWebViewState();
}

class _CustomWebViewState extends State<CustomWebView> {
  String? host;
  List<PopupMenuItem>? popupList;

  @override
  void initState() {
    super.initState();
    final uri = Uri.parse(widget.url);
    host = uri.host;
    popupList = [
      // PopupMenuItem(
      //   child: Row(
      //     children: [
      //       GestureDetector(
      //         onTap: () {
      //           if (_webViewController != null) {
      //             _webViewController!.goBack();
      //           }
      //         },
      //         child: const Icon(
      //           Icons.arrow_back_ios,
      //           color: Colors.black,
      //           size: 20,
      //         ),
      //       ),
      //       const SizedBox(
      //         width: 20,
      //       ),
      //       GestureDetector(
      //         onTap: () {
      //           if (_webViewController != null) {
      //             _webViewController!.goForward();
      //           }
      //         },
      //         child: const Icon(
      //           Icons.arrow_forward_ios,
      //           color: Colors.black,
      //           size: 20,
      //         ),
      //       ),
      //     ],
      //   ),
      // ),
      PopupMenuItem(
        child: GestureDetector(
          onTap: () {
            
            Clipboard.setData(
              ClipboardData(
                text: widget.url,
              ),
            ).then((value){
               showCustomToast(context, allMessages.value.copiedToClipboard ?? 'copied to clipboard');
            });
          },
          child: Row(
             mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(Icons.copy,size: 18),
              const SizedBox(width: 6),
              Text(
            allMessages.value.copyLink ?? 'Copy Link',
            style: const TextStyle(
               fontFamily: 'Poppins',
                fontSize: 14),
             ),
            ])
        ),
      ),
      PopupMenuItem(
        child: GestureDetector(
          onTap: () {
            canLaunchUrl(Uri.parse(widget.url));
          },
          child:  Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(Icons.web,size: 18),
              const SizedBox(width: 6),
                Text(
              allMessages.value.openBrowser ??   'Open in browser',
                style: const TextStyle(
                  fontFamily: 'Poppins',
                    fontSize: 14),
              ),
            ],
          ),
        ),
      )
    ];
  }

  @override
  Widget build(BuildContext context) {
    final heroImageUrl = "https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=800&q=80"; // Placeholder
    final adImageUrl = "https://dummyimage.com/600x100/eee/333&text=Ad+Banner"; // Placeholder
    final title = "Modern News Card UI in Flutter";
    final body = "This is a sample news article body. It demonstrates how to use a modern, clean layout with a hero image, pill tag, and a beautiful content section. \n\nPublished: 2 hours ago";
    final source = host ?? "source.com";

    return Scaffold(
      backgroundColor: Colors.grey[100],
      body: Column(
        children: [
          // Hero Image with floating menu
          Stack(
            clipBehavior: Clip.none,
            children: [
              ClipRRect(
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(24),
                  topRight: Radius.circular(24),
                ),
                child: Image.network(
                  heroImageUrl,
                  width: double.infinity,
                  height: 220,
                  fit: BoxFit.cover,
                ),
              ),
              Positioned(
                top: 40,
                right: 24,
                child: Material(
                  color: Colors.white.withOpacity(0.8),
                  shape: const CircleBorder(),
                  child: IconButton(
                    icon: const Icon(Icons.more_vert, color: Colors.black87),
                    onPressed: () {
                      showModalBottomSheet(
                        context: context,
                        shape: const RoundedRectangleBorder(
                          borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
                        ),
                        builder: (context) => Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            ListTile(
                              leading: const Icon(Icons.copy),
                              title: const Text('Copy Link'),
                              onTap: () {
                                Clipboard.setData(ClipboardData(text: widget.url));
                                Navigator.pop(context);
                              },
                            ),
                            ListTile(
                              leading: const Icon(Icons.web),
                              title: const Text('Open in browser'),
                              onTap: () async {
                                await launchUrl(Uri.parse(widget.url));
                                Navigator.pop(context);
                              },
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                ),
              ),
              // Pill tag
              Positioned(
                left: 24,
                bottom: -18,
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(50),
                    border: Border.all(color: Colors.grey[300]!),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black12,
                        blurRadius: 4,
                        offset: Offset(0, 2),
                      ),
                    ],
                  ),
                  child: const Text(
                    "appname",
                    style: TextStyle(
                      fontSize: 13,
                      fontWeight: FontWeight.w500,
                      color: Colors.black87,
                      fontFamily: 'Poppins',
                      letterSpacing: 0.2,
                    ),
                  ),
                ),
              ),
            ],
          ),
          // Content Section
          Expanded(
            child: Container(
              width: double.infinity,
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(24),
                  bottomRight: Radius.circular(24),
                ),
              ),
              padding: const EdgeInsets.fromLTRB(24, 32, 24, 12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      fontFamily: 'Poppins',
                      height: 1.3,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    body,
                    style: const TextStyle(
                      fontSize: 16,
                      color: Colors.black87,
                      height: 1.6,
                      fontFamily: 'Poppins',
                    ),
                  ),
                  const Spacer(),
                  Text(
                    "Read more at $source",
                    style: const TextStyle(
                      fontSize: 13,
                      color: Colors.grey,
                      fontStyle: FontStyle.italic,
                      fontFamily: 'Poppins',
                    ),
                  ),
                ],
              ),
            ),
          ),
          // Ad Banner
          Container(
            width: double.infinity,
            height: 90,
            margin: const EdgeInsets.only(top: 12, bottom: 8, left: 16, right: 16),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              image: DecorationImage(
                image: NetworkImage(adImageUrl),
                fit: BoxFit.cover,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black12,
                  blurRadius: 6,
                  offset: Offset(0, 2),
                ),
              ],
            ),
            child: Align(
              alignment: Alignment.bottomRight,
              child: Container(
                margin: const EdgeInsets.all(12),
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.85),
                  borderRadius: BorderRadius.circular(30),
                ),
                child: const Text(
                  "Know More",
                  style: TextStyle(
                    color: Colors.black87,
                    fontWeight: FontWeight.w600,
                    fontFamily: 'Poppins',
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
