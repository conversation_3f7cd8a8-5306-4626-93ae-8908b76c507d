name: incite
description: A new Flutter project.
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 3.0.3+7

environment:
  sdk: '>=3.5.0 <4.0.0'
  
# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  
  flutter_svg:
  google_sign_in: ^6.1.4
  preload_page_view: ^0.2.0
  easy_audience_network: 
  visibility_detector:
  share_plus:
  provider: ^6.0.5
  html: ^0.15.4
  flutter_tabler_icons: ^1.2.0
  flutter_html: ^3.0.0-beta.2 
  in_app_review: ^2.0.8
  google_mobile_ads: ^5.0.0
  get_storage:
  path_provider:
  flutter_pdfview: ^1.3.1
  the_apple_sign_in: 
  intl:
  http:
  cached_network_image:
  onesignal_flutter: ^5.3.3
  image_picker:
  flutter_inappwebview: ^6.0.0
  shared_preferences: ^2.0.13
  mvc_pattern: ^8.5.2
  get_it:
  permission_handler:
  firebase_core: ^3.6.0
  firebase_messaging: ^15.1.3
  lottie: ^2.2.0
  # package_info_plus:
  # device_info_plus: ^9.1.0
  dio:
  firebase_auth: ^5.3.1
  cupertino_icons: ^1.0.2
  url_launcher:
  firebase_analytics: ^11.3.3
  firebase_performance: ^0.10.0+8
  flutter_tts: ^4.0.2
  audioplayers:
  just_audio: ^0.9.34
  photo_view: ^0.15.0
  pinput: ^5.0.0
  flutter_custom_tabs: ^2.0.0+1
  flutter_local_notifications: ^18.0.1
  #  in_app_update: ^4.2.2
  upgrader: ^11.3.0
  speech_to_text:
  video_player: ^2.9.2
  webviewtube: ^2.1.2
  in_app_update: ^4.2.3
  # flutter_isolate:
  # injectable:
  # injectable_generator:
  # flutter_bloc:
  # wakelock_plus:
  # dart_jsonwebtoken: ^2.14.0
  # flutter_tts: ^3.2.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0
  


dependency_overrides:

  flutter_preload_videos:
    git:
      url: https://github.com/Ritikrai09/rr_video_player.git
      ref: '8dbb999'
  
  # flutter_inappwebview_ios:
  #   git:
  #     url: https://github.com/andychucs/flutter_inappwebview.git
  #     ref: master
  #     path: flutter_inappwebview_ios
# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/.
    - assets/svg/.
    - assets/lottie/.

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: Roboto
      fonts:
      - asset: assets/fonts/Roboto-Medium.ttf
        weight: 600
      - asset: assets/fonts/Roboto-Regular.ttf
        weight: 400 
      - asset: assets/fonts/Roboto-Bold.ttf  
        weight: 700

    - family: Poppins
      fonts:
        - asset: assets/fonts/Poppins-ExtraBold.ttf
          weight: 800
        - asset: assets/fonts/Poppins-Bold.ttf
          weight: 700
        - asset: assets/fonts/Poppins-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Poppins-Medium.ttf
          weight: 500
        - asset: assets/fonts/Poppins-Regular.ttf
          weight: 400
        - asset: assets/fonts/Poppins-Light.ttf
          weight: 300
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
