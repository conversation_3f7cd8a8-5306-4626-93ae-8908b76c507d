import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:incite/api_controller/blog_controller.dart';
import 'package:incite/api_controller/shorts_controller.dart';
import 'package:incite/api_controller/user_controller.dart';
import 'package:incite/main.dart';
import 'package:incite/pages/main/blog.dart';
import 'package:incite/pages/shorts_video.dart';
import 'package:incite/utils/theme_util.dart';
import 'package:incite/widgets/anim_util.dart';
import 'package:incite/widgets/custom_toast.dart';

import 'package:incite/widgets/svg_icon.dart';
import 'package:preload_page_view/preload_page_view.dart';
import 'package:provider/provider.dart';
// import 'package:tutorial_coach_mark/tutorial_coach_mark.dart';
import 'package:upgrader/upgrader.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../api_controller/app_provider.dart';
import '../../model/blog.dart';
import '../../splash_screen.dart';
import '../../utils/image_util.dart';
import 'blog_wrap.dart';
import 'home.dart';

int shortsCurrIndex = 0;

class DashboardPage extends StatefulWidget {
  const DashboardPage(
      {super.key,
      this.action,
      this.index = 0,
      this.isFromVideo = false,
      this.blog,
      this.isLoad = true,
      this.bottomIndex = 0,
      this.fromInitial = false});

  final int index, bottomIndex;
  final Blog? blog;
  final bool isLoad;
  final BlogOptionType? action;
  final bool fromInitial, isFromVideo;

  @override
  State<DashboardPage> createState() => _DashboardPageState();
}

class _DashboardPageState extends State<DashboardPage>
    with AutomaticKeepAliveClientMixin, WidgetsBindingObserver {
  late PageController controller, shortsHomeController;
  PreloadPageController preloadPageController = PreloadPageController();
  bool data = true;
  late int currIndex;
  UserProvider user = UserProvider();
  int curr = 0;
  // TutorialCoachMark? tutorialCoachMark;
  Timer? timer;

  var keyButton = GlobalKey();
  var keyButton2 = GlobalKey();
  var keyButton3 = GlobalKey();

  var keyButton4 = GlobalKey();

  GlobalKey<NavigatorState> navigatorkey = GlobalKey<NavigatorState>();

  // void createTutorial() {
  //   tutorialCoachMark = TutorialCoachMark(
  //     targets: createTarget(), // List<TargetFocus>
  //     colorShadow:
  //         hexToRgb(allSettings.value.primaryColor), // DEFAULT Colors.black
  //     // alignSkip: Alignment.bottomRight,
  //     // textSkip: "SKIP",
  //     // paddingFocus: 10,
  //     // opacityShadow: 0.8,
  //     onClickTarget: (target) {
  //       // print();
  //     },
  //     onClickTargetWithTapPosition: (target, tapDetails) {
  //       print("target: $target");
  //       print(
  //           "clicked at position local: ${tapDetails.localPosition} - global: ${tapDetails.globalPosition}");
  //     },
  //     onClickOverlay: (target) {},
  //     onSkip: () {
  //       currentUser.value.isNewUser = false;
  //       prefs!.setBool('is_tutorial_taken', true);
  //       setState(() {});
  //       return true;
  //     },
  //     onFinish: () {
  //       currentUser.value.isNewUser = false;
  //       prefs!.setBool('is_tutorial_taken', true);
  //       setState(() {});
  //     },
  //   );
  // }

  // void showTutorial() {
  //   tutorialCoachMark!.show(context: context);
  // }

  @override
  void initState() {
    currIndex = widget.index;
    shortsCurrIndex = widget.bottomIndex;
    prefs!.remove('id');
    controller = PageController(initialPage: widget.index);
    shortsHomeController = PageController(initialPage: widget.bottomIndex);
    WidgetsBinding.instance.addObserver(this);
    // if (!prefs!.containsKey('is_tutorial_taken')) {
    // if(currentUser.value.isNewUser ==  true){
    // createTutorial();
    // Future.delayed(const Duration(milliseconds: 1000), showTutorial);
    // }
    // }
    timer = Timer.periodic(const Duration(seconds: 15), (timer) {
      var provider = Provider.of<AppProvider>(context, listen: false);
      _fetchData(provider);

     
        getAdsAnalytics(provider);
      
    });

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
      // var preloadProvider =
      //     Provider.of<PreloadProvider>(context, listen: false);

      var provider = Provider.of<AppProvider>(context, listen: false);
      if(widget.index == 1){
        blogListHolder.setBlogType(BlogType.allnews);
      }
      user.socialMediaList();
      UserProvider().getCMS(context);
      if (widget.isLoad == true) {
        provider.getCategory();
      }
      if (widget.isFromVideo == false) {
        if (allSettings.value.isShortEnable == '1') {
          await ShortsApi().fetchShorts(context).then((value) {
            setState(() {});
          }).onError((e, r) {});
        }
      }

      if (currentUser.value.id != null) {
        if (!prefs!.containsKey('isBookmark')) {
          provider.getAllBookmarks().then((DataModel? value) {});
        } else {
          provider.setAllBookmarks();
        }
      }
      setState(() {});
    });

    super.initState();
  }

  // List<TargetFocus> createTarget() {
  //   List<TargetFocus> targets = [];
  //   targets
  //       .add(TargetFocus(identify: "Target 1", keyTarget: keyButton, contents: [
  //     TargetContent(
  //         align: ContentAlign.bottom,
  //         child: const Column(
  //           mainAxisSize: MainAxisSize.min,
  //           crossAxisAlignment: CrossAxisAlignment.start,
  //           children: <Widget>[
  //             Text(
  //               "Swipe Down",
  //               style: TextStyle(
  //                   fontWeight: FontWeight.bold,
  //                   color: Colors.white,
  //                   fontSize: 20.0),
  //             ),
  //             Padding(
  //               padding: EdgeInsets.only(top: 10.0),
  //               child: Text(
  //                 "To See the Previous Story",
  //                 style: TextStyle(color: Colors.white),
  //               ),
  //             )
  //           ],
  //         ))
  //   ]));

  //   targets.add(
  //       TargetFocus(identify: "Target 2", keyTarget: keyButton2, contents: [
  //     TargetContent(
  //         align: ContentAlign.bottom,
  //         child: const Column(
  //           mainAxisSize: MainAxisSize.min,
  //           crossAxisAlignment: CrossAxisAlignment.start,
  //           children: <Widget>[
  //             Text(
  //               "Swipe Up",
  //               style: TextStyle(
  //                   fontWeight: FontWeight.bold,
  //                   color: Colors.white,
  //                   fontSize: 20.0),
  //             ),
  //             Padding(
  //               padding: EdgeInsets.only(top: 10.0),
  //               child: Text(
  //                 "To See Next Story",
  //                 style: TextStyle(color: Colors.white),
  //               ),
  //             )
  //           ],
  //         )),
  //   ]));

  //   targets.add(
  //       TargetFocus(identify: "Target 3", keyTarget: keyButton3, contents: [
  //     TargetContent(
  //         align: ContentAlign.bottom,
  //         child: const Column(
  //           mainAxisSize: MainAxisSize.min,
  //           crossAxisAlignment: CrossAxisAlignment.end,
  //           children: <Widget>[
  //             Text(
  //               "Swipe Left",
  //               style: TextStyle(
  //                   fontWeight: FontWeight.bold,
  //                   color: Colors.white,
  //                   fontSize: 20.0),
  //             ),
  //             Padding(
  //               padding: EdgeInsets.only(top: 10.0),
  //               child: Text(
  //                 "To Read Full Story",
  //                 style: TextStyle(color: Colors.white),
  //               ),
  //             )
  //           ],
  //         ))
  //   ]));

  //   targets.add(
  //       TargetFocus(identify: "Target 4", keyTarget: keyButton4, contents: [
  //     TargetContent(
  //         align: ContentAlign.bottom,
  //         child: const Column(
  //           mainAxisSize: MainAxisSize.min,
  //           crossAxisAlignment: CrossAxisAlignment.start,
  //           children: <Widget>[
  //             Text(
  //               "Swipe Right",
  //               style: TextStyle(
  //                   fontWeight: FontWeight.bold,
  //                   color: Colors.white,
  //                   fontSize: 20.0),
  //             ),
  //             Padding(
  //               padding: EdgeInsets.only(top: 10.0),
  //               child: Text(
  //                 "To Visit Dashbaord",
  //                 style: TextStyle(color: Colors.white),
  //               ),
  //             )
  //           ],
  //         ))
  //   ]));
  //   return targets;
  // }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    // Handle the lifecycle state change
    var provider = Provider.of<AppProvider>(context, listen: false);

    switch (state) {
      case AppLifecycleState.resumed:
        timer = Timer.periodic(const Duration(seconds: 15), (timer) {
          _fetchData(provider);
           getAdsAnalytics(provider);
        });
        setState(() {});
        break;

      case AppLifecycleState.paused:
        if (timer != null) {
          timer!.cancel();
        }
        _fetchData(provider);

        getAdsAnalytics(provider);

        break;
      case AppLifecycleState.inactive:
        if (timer != null) {
          timer!.cancel();
        }
        _fetchData(provider);
        getAdsAnalytics(provider);
        //setState(() {   });
        break;
      case AppLifecycleState.detached:
        prefs!.remove('isBookmark');
        if (timer != null) {
          timer!.cancel();
        }
        _fetchData(provider);
        getAdsAnalytics(provider);

        break;
      case AppLifecycleState.hidden:
        // timer?.cancel();
        // _fetchData();
        // setState(() {   });
        break;
    }
  }

  void _fetchData(AppProvider provider) {
    provider.getAnalyticData();
  }

  void getAdsAnalytics(AppProvider provider) {
     if ( provider.adAnalytics[0]["ads_ids"].isNotEmpty ||
              provider.adAnalytics[3]["shorts_ids"].isNotEmpty ||  
             provider.adAnalytics[2]["shorts_ids"].isNotEmpty 
             ||  provider.adAnalytics[1]["ads_ids"].isNotEmpty ) {
    provider.getAnalyticData(isAds: true);
             }
  }

  @override
  void dispose() {
    super.dispose();
    WidgetsBinding.instance.removeObserver(this);
    if (timer != null) {
      timer!.cancel();
    }
  }

    void redirectToPlayStore() async {

  // Create the Play Store deep link URL
  final String url = Platform.isAndroid ? 
  allSettings.value.playStoreUrl.toString()
  : allSettings.value.appStoreUrl.toString();

  if (await canLaunchUrl(Uri.parse(url))) {
    await launchUrl(Uri.parse(url));
  } else {
    CustomToast(message :'Could not redirect to $url',onDismiss: () {});
    throw 'Could not launch $url';
  }
}

  @override
  Widget build(BuildContext context) {
    super.build(context);
    var appProvider = Provider.of<AppProvider>(context, listen: false);
    //  log("!prefs!.containsKey('update_duration') && upgrader != null).toString()");
    // log((!prefs!.containsKey('update_duration') && upgrader != null).toString());
    return UpgradeAlert(
        navigatorKey: navigatorkey,
        upgrader: !prefs!.containsKey('update_duration') && upgrader != null
            ? upgrader
            : Upgrader(durationUntilAlertAgain: const Duration(days: 3)),
        onIgnore: () {
          prefs!.setString('update_duration', DateTime.now().toIso8601String());
          setState(() {});
          return true;
        },
        onUpdate: (){
          redirectToPlayStore();
          return false;
        },
        onLater: () {
          prefs!.setString('update_duration', DateTime.now().toIso8601String());
          setState(() {});
          return true;
        },
        shouldPopScope: () => Platform.isAndroid
            ? allSettings.value.isAndroidForceUpdate != '1'
            : allSettings.value.isIosForceUpdate != '1',
        showIgnore: false,
        showLater: Platform.isAndroid
            ? allSettings.value.isAndroidForceUpdate != '1'
            : allSettings.value.isIosForceUpdate != '1',
        child: ValueListenableBuilder(
            valueListenable: allSettings,
            builder: (context, value, child) {
              var list = [
                [ allMessages.value.dashboard ?? "Home", "assets/svg/dash.svg"],
                [ allMessages.value.shorts ?? "Shorts", "assets/svg/shorts.svg"]
              ];
              return value.enableMaintainanceMode == '1'
                  ? NavigatorPopHandler(
                      enabled: false,
                      onPop: () async {
                        showCustomDialog(
                            context: context,
                            title: allMessages.value.confirmExitTitle ??
                                "Exit Application",
                            text: allMessages.value.confirmExitApp ??
                                'Do you want to exit from app ?',
                            onTap: () {
                              var provider = Provider.of<AppProvider>(context,
                                  listen: false);
                              var end = DateTime.now();
                              provider.addAppTimeSpent(
                                  startTime: provider.appStartTime,
                                  endTime: end);
                              provider.getAnalyticData(isAds: true);
                              provider.getAnalyticData(isAds: false);
                              Future.delayed(const Duration(milliseconds: 300));
                              exit(0);
                            },
                            isTwoButton: true);
                        // return false;
                      },
                      child: Material(
                        child: SizedBox(
                          width: size(context).width,
                          child: Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 24),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                // Image.asset(Img.logo,width: 100, height: 100),
                                Stack(children: [
                                  Image.asset('assets/images/maintain.png',
                                      width: 200, height: 200),
                                  Positioned(
                                      top: kToolbarHeight,
                                      right: 50,
                                      child: Image.asset(Img.logo,
                                          width: 30, height: 30))
                                ]),
                                Text(value.maintainanceTitle.toString(),
                                    textAlign: TextAlign.center,
                                    style: const TextStyle(
                                        fontFamily: 'Roboto',
                                        fontSize: 24,
                                        fontWeight: FontWeight.bold)),
                                const SizedBox(height: 12),
                                Text(value.maintainanceShortText.toString(),
                                    textAlign: TextAlign.center,
                                    style: const TextStyle(
                                        fontFamily: 'Roboto',
                                        fontSize: 14,
                                        fontWeight: FontWeight.w400)),
                              ],
                            ),
                          ),
                        ),
                      ),
                    )
                  : PopScope(
                      canPop: false,
                      onPopInvoked: (val) async {
                        if ((Platform.isAndroid &&
                                allSettings.value.isAndroidForceUpdate ==
                                    '1') ||
                            (Platform.isIOS &&
                                allSettings.value.isIosForceUpdate == '1')) {
                          // navigatorkey.currentState!.overlay!.activate();
                          return;
                        } else if (MediaQuery.of(context).orientation ==
                            Orientation.landscape) {
                          SystemChrome.setEnabledSystemUIMode(
                              SystemUiMode.manual,
                              overlays: [
                                SystemUiOverlay.top,
                                SystemUiOverlay.bottom
                              ]);
                          SystemChrome.setPreferredOrientations([
                            DeviceOrientation.portraitUp,
                            DeviceOrientation.portraitDown,
                          ]);
                        } else if (shortsCurrIndex == 1 &&
                            MediaQuery.of(context).orientation ==
                                Orientation.portrait) {
                          shortsHomeController.animateToPage(0,
                              duration: const Duration(milliseconds: 200),
                              curve: Curves.easeIn);
                          shortsCurrIndex = 0;
                          setState(() {});
                        } else if (currIndex == 1 &&
                            MediaQuery.of(context).orientation ==
                                Orientation.portrait) {
                          controller.animateToPage(0,
                              duration: const Duration(milliseconds: 200),
                              curve: Curves.easeIn);
                          currentUser.value.isNewUser = false;
                          prefs!.setBool('is_tutorial_taken', true);
                          setState(() {});
                        } else {
                          showCustomDialog(
                              context: context,
                              title: allMessages.value.confirmExitTitle ??
                                  "Exit Application",
                              text: allMessages.value.confirmExitApp ??
                                  'Do you want to exit from app ?',
                              onTap: () {
                                var provider = Provider.of<AppProvider>(context,
                                    listen: false);
                                var end = DateTime.now();
                                provider.addAppTimeSpent(
                                    startTime: provider.appStartTime,
                                    endTime: end);
                                provider.getAnalyticData();
                                Future.delayed(
                                    const Duration(milliseconds: 300));
                                exit(0);
                              },
                              isTwoButton: true);
                        }
                      },
                      child: AnnotatedRegion(
                        value: SystemUiOverlayStyle(
                            statusBarIconBrightness: dark(context)
                                ? Brightness.light
                                : Brightness.dark,
                            statusBarColor: Colors.transparent),
                        child: Scaffold(
                          body: Stack(
                            children: [
                              PageView(
                                controller: shortsHomeController,
                                physics: const NeverScrollableScrollPhysics(),
                                children: [
                                  PageView(
                                    physics: (Platform.isAndroid &&
                                                allSettings.value
                                                        .isAndroidForceUpdate ==
                                                    '1') ||
                                            (Platform.isIOS &&
                                                allSettings.value
                                                        .isIosForceUpdate ==
                                                    '1')
                                        ? const NeverScrollableScrollPhysics()
                                        : MediaQuery.of(context).orientation ==
                                                Orientation.landscape
                                            ? const NeverScrollableScrollPhysics()
                                            : null,
                                    controller: controller,
                                    onPageChanged: (value) {
                                      currIndex = value;
                                      setState(() {});
                                    },
                                    children: [
                                      HomePage(
                                          menuTapped: (value) {
                                            currIndex = value == true ? 1 : 0;
                                            setState(() {});
                                          },
                                          onChanged: backToHome),
                                      Stack(
                                        children: [
                                          BlogWrapPage(
                                              //   key: ValueKey("${blogListHolder.blogType}$curr"),
                                              preloadPageController:
                                                  preloadPageController,
                                              index: curr,
                                              type: widget.action,
                                              onChanged: (value) {
                                                controller.animateToPage(0,
                                                    duration: const Duration(
                                                        milliseconds: 300),
                                                    curve: Curves.easeIn);
                                              }),
                                          !prefs!.containsKey(
                                                  'is_tutorial_taken')
                                              ? Positioned(
                                                  left: 0,
                                                  right: 0,
                                                  child: instructions(context))
                                              : const SizedBox(),
                                        ],
                                      ),
                                      //  if(blogListHolder.getList().blogs.isNotEmpty && blogListHolder.getList().blogs[blogListHolder.getIndex()].sourceLink !=null)
                                      //    CustomWebView(url: blogListHolder.getList().blogs[blogListHolder.getIndex()].sourceLink.toString())
                                    ],
                                  ),
                                  if (allSettings.value.isShortEnable == '1')
                                    (shortsCurrIndex == 0
                                        ? const SizedBox()
                                        : Container(
                                            color: shortsCurrIndex == 1
                                                ? Colors.black
                                                : null,
                                            padding: const EdgeInsets.only(
                                                bottom: 80),
                                            child: PreloadVideoPage(
                                              isFromVideo: widget.isFromVideo,
                                              focusedIndex:
                                                  appProvider.focusedIndex,
                                              onHomeTap: () {
                                                shortsHomeController
                                                    .jumpToPage(0);
                                              },
                                              // focusedIndex:
                                              //     preloadprovider.focusedIndex,
                                              backTap: () {
                                                shortsHomeController.jumpToPage(0);
                                                shortsCurrIndex = 0;
                                                // shortsHomeController.jumpToPage(0);
                                                // shortsCurrIndex = 0;
                                                // var provider =
                                                //     Provider.of<PreloadProvider>(
                                                //         context,
                                                //         listen: false);
                                                // provider.pauseVideoAtIndex(
                                                //     provider.focusedIndex);
                                                 setState(() {});
                                              },
                                            ),
                                          ))
                                ],
                              ),
                              if (allSettings.value.isShortEnable == '1')
                                bottomNav(context, list)
                            ],
                          ),
                        ),
                      ),
                    );
            }));
  }

  Widget bottomNav(BuildContext context, List<List<String>> list) {
    return ValueListenableBuilder(
        valueListenable: allSettings,
        builder: (context, value, child) {
          return value.isShortEnable == '0'
              ? const SizedBox()
              : Positioned(
                  left: 16,
                  right: 16,
                  bottom: 16,
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(100),
                      color: shortsCurrIndex == 1
                          ? Colors.black
                          : Theme.of(context).cardColor,
                    ),
                    child: AnimatedContainer(
                      duration: const Duration(milliseconds: 300),
                      curve: Curves.easeIn,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(100),
                          color: shortsCurrIndex == 1
                              ? const Color.fromRGBO(20, 20, 20, 1)
                              : Theme.of(context).cardColor,
                          boxShadow: const [
                            BoxShadow(
                                offset: Offset(15, 15),
                                blurRadius: 30,
                                spreadRadius: 0,
                                color: Color.fromRGBO(0, 0, 0, 0.08))
                          ]),
                      height: currIndex == 0 ? 50 : 0,
                      width: size(context).width,
                      alignment: Alignment.center,
                      // margin: const EdgeInsets.all(12),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          ...list.asMap().entries.map((e) {
                            var linearGradient = LinearGradient(
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                              colors:  [
                              Colors.white,
                               dark(context)? Colors.white:Theme.of(context).primaryColor
                            ]);
                            return Expanded(
                              key: ValueKey(e),
                              child: Padding(
                                padding: const EdgeInsets.all(4.0),
                                child: AnimationFadeSlide(
                                  dx: 0,
                                  key: ValueKey("${e.key}"),
                                  child: AnimatedContainer(
                                    duration: const Duration(milliseconds: 300),
                                    height: currIndex == 0 ? 65 : 0,
                                    curve: Curves.easeInSine,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(100),
                                      color: shortsCurrIndex == e.key
                                          ? Theme.of(context)
                                              .primaryColor.withOpacity(0.33)
                                          : null,
                                      // border:shortsCurrIndex == e.key
                                      //   ? Border(top: BorderSide(width: 2,
                                      //     color: Theme.of(context).primaryColor))
                                      //   : null
                                    ),
                                    child: InkWell(
                                        onTap: () {
                                          shortsHomeController
                                              .jumpToPage(e.key);
                                          shortsCurrIndex = e.key;
                                          setState(() {});
                                        },
                                        child: Row(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.center,
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            SvgIcon(
                                              e.value[1],
                                              width: 24,
                                              color: shortsCurrIndex == e.key
                                                  ? dark(context)||  shortsCurrIndex ==1
                                                      ? Colors.white
                                                      : Theme.of(context)
                                                          .primaryColor
                                                  : Colors.grey,
                                            ),
                                            const SizedBox(width: 8),
                                            Text(e.value[0],
                                                    style: TextStyle(
                                                        fontSize: 14,
                                                        fontFamily: 'Roboto',
                                                        fontWeight: shortsCurrIndex ==
                                                                e.key
                                                            ?FontWeight
                                                                    .w600 : FontWeight
                                                                    .w400,
                                                        color: shortsCurrIndex ==
                                                                e.key
                                                            ? dark(context) ||  shortsCurrIndex ==1
                                                                ? Colors.white
                                                                : Theme.of(
                                                                        context)
                                                                    .primaryColor
                                                            : Colors.grey)),
                                          ],
                                        )),
                                  ),
                                ),
                              ),
                            );
                          })
                        ],
                      ),
                    ),
                  ),
                );
        });
  }

  GestureDetector instructions(BuildContext context) {
    return GestureDetector(
      onVerticalDragStart: (details) {
        prefs!.setBool('is_tutorial_taken', true);
        setState(() {});
      },
      onHorizontalDragStart: (details) {
        prefs!.setBool('is_tutorial_taken', true);
        setState(() {});
      },
      onTap: () {
        prefs!.setBool('is_tutorial_taken', true);
        setState(() {});
      },
      child: Container(
        height: size(context).height,
        color: Colors.black.withOpacity(0.9),
        padding: const EdgeInsets.symmetric(horizontal: 30),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text(
              "Swipe Down",
              style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                  fontSize: 15.0),
            ),
            const Padding(
              padding: EdgeInsets.only(top: 10.0),
              child: Text(
                "To See the Previous Story",
                style: TextStyle(color: Colors.white, fontSize: 12),
              ),
            ),
            const SizedBox(height: 40),
            AnimatedArrow(
                key: keyButton,
                icon: Icons.arrow_downward_rounded,
                dy: -0.5,
                dx: 0),
            SizedBox(
              height: size(context).height / 4,
              child: Row(
                children: [
                  Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      AnimatedArrow(
                          key: keyButton4,
                          icon: Icons.arrow_back_rounded,
                          dx: 0.5),
                      const SizedBox(height: 20),
                      const Text(
                        "Swipe Left",
                        style: TextStyle(
                            fontWeight: FontWeight.w600,
                            color: Colors.white,
                            fontSize: 15.0),
                      ),
                      const Padding(
                        padding: EdgeInsets.only(top: 10.0),
                        child: Text(
                          "To Read Full Story",
                          style: TextStyle(color: Colors.white, fontSize: 14),
                        ),
                      )
                    ],
                  ),
                  const Spacer(),
                  Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      AnimatedArrow(
                          key: keyButton3,
                          icon: Icons.arrow_forward_rounded,
                          dx: -0.5),
                      const SizedBox(height: 20),
                      const Text(
                        "Swipe Right",
                        style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                            fontSize: 15.0),
                      ),
                      const Padding(
                        padding: EdgeInsets.only(top: 10.0),
                        child: Text(
                          "To Visit Dashbaord",
                          style: TextStyle(color: Colors.white, fontSize: 12),
                        ),
                      )
                    ],
                  ),
                ],
              ),
            ),
            //  --------------  Swipe Up Guide ---------
            AnimatedArrow(
                key: keyButton2,
                icon: Icons.arrow_upward_rounded,
                dy: 0.5,
                dx: 0),
            const SizedBox(height: 40),
            const Text(
              "Swipe Up",
              style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                  fontSize: 15.0),
            ),
            const Padding(
              padding: EdgeInsets.only(top: 10.0),
              child: Text(
                "To See the Next Story",
                style: TextStyle(color: Colors.white, fontSize: 12),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void backToHome(int value) async {
    data = false;
    curr = value;
    if (preloadPageController.hasClients) {
      preloadPageController.jumpToPage(curr);
    }
    controller.animateToPage(1,
        duration: const Duration(milliseconds: 300), curve: Curves.easeIn);
    setState(() {});
  }

  @override
  bool get wantKeepAlive => data;
}

class AnimatedArrow extends StatefulWidget {
  const AnimatedArrow({super.key, required this.icon, this.dx, this.dy});

  final IconData icon;
  final double? dx, dy;

  @override
  State<AnimatedArrow> createState() => _AnimatedArrowState();
}

class _AnimatedArrowState extends State<AnimatedArrow> {
  bool isPlay = false;

  @override
  void initState() {
    isPlay = true;
    setState(() {});
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return AnimationFadeSlide(
      curve: Curves.easeIn,
      dy: widget.dy ?? 0,
      dx: widget.dx ?? -0.5,
      duration: 700,
      isFade: false,
      play: isPlay,
      repeat: true,
      child: Container(
        width: 48,
        height: 48,
        decoration: BoxDecoration(
            shape: BoxShape.circle,
            border: Border.all(width: 1, color: Colors.white)),
        child: Icon(widget.icon, color: Colors.white),
      ),
    );
  }
}
