import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:lottie/lottie.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:incite/api_controller/blog_controller.dart';
import 'package:incite/api_controller/user_controller.dart';
import 'package:incite/model/blog.dart';
import 'api_controller/app_provider.dart';

SharedPreferences? prefs;

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key,this.isNotificationClick=false});
  final bool isNotificationClick;

  @override
  State<SplashScreen> createState() => _SplashScreenState();    
}

class _SplashScreenState extends State<SplashScreen> {

  @override
  void initState() {
    super.initState();
    if (widget.isNotificationClick == false) {
      startCall();
    }
  }

  Future startCall() async {
    var of = Provider.of<AppProvider>(context, listen: false);
    var user = UserProvider();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      of.setAnalyticData();
      if (!prefs!.containsKey('local_data')) {
        if (allSettings.value.enableMaintainanceMode != '1') {
          user.getLanguageFromServer(context).then((value) async {
            if (currentUser.value.id != null &&
                !prefs!.containsKey('setNotification')) {
              of
                  .getCategory(
                      allowUpdate: false,
                      deepLink: !prefs!.containsKey('setNotification'))
                  .whenComplete(switchToPage);
            } else {
              switchToPage();
            }
          });
        }
      } else {
        of
            .getCategory(deepLink: !prefs!.containsKey('setNotification'))
            .then((value) {
          Future.delayed(const Duration(milliseconds: 2000), () {
            if (!prefs!.containsKey('setNotification')) {
              if (currentUser.value.id != null) {
                blogListHolder.setList(of.feed ?? DataModel());
                blogListHolder.setBlogType(BlogType.feed);
              } else {
                blogListHolder.setList(of.allNews ?? DataModel());
                blogListHolder.setBlogType(BlogType.allnews);
              }
              if (mounted) {
                setState(() {});
              }
              switchToPage();
            }
          });
        });
      }
    });
  }

  FutureOr<void> switchToPage() {
    if (prefs!.containsKey('is_first_time_open')) {
      Navigator.pushNamedAndRemoveUntil(context, '/MainPage',arguments:1,(route) => false);
    } else {
      Navigator.pushNamedAndRemoveUntil(context, '/LoginPage',(route) => false,arguments: false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnnotatedRegion(
      value: SystemUiOverlayStyle(
        statusBarIconBrightness: Theme.of(context).brightness == Brightness.dark ? Brightness.light : Brightness.dark,
        statusBarColor: Colors.transparent),
      child: Scaffold(
        body: SafeArea(
          child: Center(
            child: Lottie.asset(
              'assets/lottie/app.json',
            ),
          ),
        ),
      ),
    );
  }
}
