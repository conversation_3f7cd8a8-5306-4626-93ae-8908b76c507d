import 'dart:io';

import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:http/http.dart' as http;

const String images= 'assets/images/';
const String svg = 'assets/svg/';

class SvgImg {
  
  static String mail = '${svg}mail.svg';
  static String password= '${svg}mail.svg';
  static String phone = '${svg}phone.svg';
  static String menu = '${svg}menu.svg';
  static String search = '${svg}search.svg';
  static String bookmark = '${svg}bookmark_post.svg';
   static String play = '${svg}play.svg';
  static String share = '${svg}share.svg';
  static String lock = '${svg}lock.svg';
    static String themeMode = '${svg}theme-mode.svg';
  static String arrowRight = '${svg}arrow_right.svg';


    static String noti = '${svg}noti.svg';

    static String notiOutline = '${svg}noti-outline.svg';
    static String font = '${svg}font.svg';
     static String lang = '${svg}lang.svg';

  static String dash = '${svg}dash.svg';

  static String dash2 = '${svg}dash2.svg';
  static String setting = '${svg}setting.svg';
  static String logout = '${svg}logout.svg';
  static String star = '${svg}star.svg';
   static String profile = '${svg}profile.svg';
   static String fillBook = '${svg}book-fill.svg';
}


class Img {
  
  static String logo = '${images}app_icon.png';
  static String img1 = '${images}img1.jpg';
  static String img2 = '${images}img2.jpg';
  static String livenews = '${images}live.png';
  static String tv = '${images}tv.png';
  static String enews = '${images}enews.png';
  

  static String post1 = '${images}post1.png';
  static String post2 = '${images}post2.png';
  static String cat1 = '${images}cat1.jpg';
  static String cat2 = '${images}cat2.jpg';
  static String cat3 = '${images}cat3.jpg';
  static String cat4 = '${images}cat4.jpg';
}


Future<XFile?> downloadImage(String url) async {
    try {
      // Fetch the image from the URL
      final response = await http.get(Uri.parse(url));

      if (response.statusCode == 200) {
        // Get the temporary directory
        final directory = await getTemporaryDirectory();
        
        // Create a file in the temporary directory
        final filePath = '${directory.path}/downloaded_image.png';
        final file = File(filePath);

        // Write the image bytes to the file
        await file.writeAsBytes(response.bodyBytes);

        // Return an XFile from the file
        return XFile(file.path);
      } else {
        print('Failed to download image. Status code: ${response.statusCode}');
      }
    } catch (e) {
      print('Error downloading image: $e');
    }
    return null;
  }