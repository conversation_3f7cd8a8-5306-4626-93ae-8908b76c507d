import 'dart:developer';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:incite/api_controller/user_controller.dart';
import 'package:incite/model/blog.dart';
import 'package:incite/pages/main/dashboard.dart';
import 'package:incite/pages/main/widgets/share.dart';
import 'package:incite/splash_screen.dart';
import 'package:incite/urls/url.dart';
import 'package:incite/utils/image_util.dart';
import 'package:incite/widgets/incite_video_player.dart';
import 'package:incite/widgets/live_news.dart';
import 'package:incite/widgets/loader.dart';
import 'package:share_plus/share_plus.dart';
import '../../api_controller/news_repo.dart';
import '../../utils/color_util.dart';
import '../../utils/theme_util.dart';
import '../../widgets/anim_util.dart';
import '../../widgets/back.dart';

class LiveNews extends StatefulWidget {
  const LiveNews({super.key, this.id});

  final int? id;

  @override
  // ignore: library_private_types_in_public_api
  _LiveNewsState createState() => _LiveNewsState();
}

class _LiveNewsState extends State<LiveNews> {
  bool isFullscreen = false;
  int? selectedVideo;

   bool isShare = false;
  // CachedVideoPlayerPlusController? _controller;
  
  String? errorMessage;

  @override
  void initState() {
    super.initState();

    // if (widget.id != null) {
    //   isShare = true;
    // } else {
    //   isShare = false;
    // }

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
       await getliveNews().then((value) async {
          //setVideo();
          if (widget.id != null && value.isNotEmpty) {
            for (var i = 0; i < value.length; i++) {
              log(widget.id.toString());
              log(i.toString());
              if (widget.id == value[i].id) {
                selectedVideo = i;
                isShare = false;
                // _controller = await CacheVideoController().playYoutubeVideo(
                //     url:  liveNews[i].url ?? "",
                //     isLive: true,
                //     cacheDuration: const Duration(days: 0));
                //     Future.delayed(const Duration(seconds: 8),(){
                //     if(_controller == null){
                //       errorMessage = "Live Stream ended!";
                //       setState(() { });
                //     }
                //   });
                   setState(() { });
                 return;
              }
            }
          } else {
            selectedVideo = 0;
            // _controller = await CacheVideoController().playYoutubeVideo(
            //     url: liveNews[0].url ?? "",
            //     isLive: true,
            //     cacheDuration: const Duration(days: 0));
            //     Future.delayed(const Duration(seconds: 8),(){
            //      if(_controller == null){
            //       errorMessage = "Live Stream ended!";
            //       setState(() { });
            //     }
            //   });
            isShare = false;
            setState(() {});
          }
        });
      // }
    });
  }

  // setVideo() {
  //   final videoId = convert.YoutubePlayer.convertUrlToId(
  //     liveNews[selectedVideo!.toInt()].url
  //         .toString(),
  //   );
  //   _controller = convert.YoutubePlayerController(
  //     initialVideoId: videoId.toString(),
  //     flags: const convert.YoutubePlayerFlags(autoPlay: true),
  //   );
  //   _controller!.load(videoId.toString());

  // _controller!.addListener(() {
  //   if (_controller != null) {
  //     _enableFullscreen(_controller!.value.isFullScreen);
  //   }
  // });
  // }

  //void _enableFullscreen(bool fullscreen) {
  // isFullscreen = fullscreen;
  // setState(() {});
  // if (fullscreen == true) {
  //   // Force landscape orientation for fullscreen
  //   SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
  //   SystemChrome.setPreferredOrientations([
  //     DeviceOrientation.landscapeLeft,
  //     DeviceOrientation.landscapeRight,
  //   ]);
  // }
  // else {
  //   // Force portrait
  //   SystemChrome.setEnabledSystemUIMode(SystemUiMode.manual,
  //       overlays: [SystemUiOverlay.top, SystemUiOverlay.bottom]);
  //   SystemChrome.setPreferredOrientations([
  //     DeviceOrientation.portraitUp,
  //     DeviceOrientation.portraitDown,
  //   ]);
  // }
  //}

  @override
  Widget build(BuildContext context) {
    return
        // PopScope(
        // canPop: !(isFullscreen == true || ( _controller != null && _controller!.value.isFullScreen == true)),
        // onPopInvoked: (val) async {

        //   if (isFullscreen == true || ( _controller != null && _controller!.value.isFullScreen == true)) {
        //     SystemChrome.setEnabledSystemUIMode(SystemUiMode.manual,
        //         overlays: [SystemUiOverlay.top, SystemUiOverlay.bottom]);
        //     // _enableFullscreen(false);
        //     _controller!.toggleFullScreenMode();
        //     isFullscreen = false;
        //     setState(() {});
        //   } else {
        //     return;
        //   }
        // },
        //  child:
        CustomLoader(
      isLoading: isShare,
      child: PopScope(
        canPop: false,
        onPopInvokedWithResult: (didPop,F){
          if(didPop){
            return;
          }
           if (!prefs!.containsKey('id')) {
            Navigator.pop(context);
           }else if(widget.id != null && prefs!.containsKey('id')){
              prefs!.remove('id');
               Navigator.pushAndRemoveUntil(context, MaterialPageRoute(builder: (context)=> const DashboardPage(index: 0)),(route)=> false);
          }
        },
        child: MediaQuery.removePadding(
          context: context,
          removeTop: isFullscreen,
          removeLeft: true,
          removeRight: true,
          removeBottom: true,
          child: Scaffold(
            appBar: isFullscreen == true
                ? null
                : AppBar(
                    centerTitle: false,
                    leadingWidth: 0,
                    backgroundColor:
                        Theme.of(context).appBarTheme.backgroundColor,
                    automaticallyImplyLeading: false,
                    titleSpacing: 24,
                    toolbarHeight: 60,
                    elevation: 0,
                    title: Row(
                      children: [
                         Backbut(onTap: (){
                          if(widget.id != null && prefs!.containsKey('id')){
                            prefs!.remove('id');
                            Navigator.pushAndRemoveUntil(context, MaterialPageRoute(builder: (context)=> const DashboardPage(index: 0)),(route)=> false);
                          } else {
                            Navigator.pop(context);
                          }
                        }),
                        const SizedBox(width: 15),
                        AnimationFadeSlide(
                          dx: 0.3,
                          duration: 500,
                          child: Text(allMessages.value.liveNews ?? 'Live News',
                              style: TextStyle(
                                  fontFamily: 'Roboto',
                                  fontSize: 20,
                                  color: dark(context)
                                      ? ColorUtil.white
                                      : ColorUtil.textblack,
                                  fontWeight: FontWeight.w600)),
                        )
                      ],
                    ),
                  ),
            body: liveNews.isEmpty 
                ? SingleChildScrollView(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 24, vertical: 24),
                      child: Column(
                        children: [
                          ...List.generate(8, (index) => const ListShimmer())
                        ],
                      ),
                    ),
                  )
                : Column(
                    children: [
                      //video player
                      //  _controller == null 
                      //     ? SizedBox(
                      //       key: ValueKey(selectedVideo),
                      //         height: height10(context) * 25,
                      //         width: MediaQuery.of(context).size.width,
                      //         child: Stack(
                      //           children: [
                      //             Positioned.fill(
                      //                 child: CachedNetworkImage(
                      //                     key: selectedVideo != null
                      //                         ? ValueKey(
                      //                             "${liveNews[selectedVideo ?? 0].id}${liveNews[selectedVideo ?? 0].image}")
                      //                         : ValueKey(
                      //                             "${liveNews[0].id}${liveNews[0].image}"),
                      //                     height: height10(context) * 25,
                      //                     imageUrl: liveNews[selectedVideo ?? 0]
                      //                             .image ??
                      //                         "",
                      //                     fit: BoxFit.cover)),
                                  // if (_controller == null)
                                    // Positioned.fill(
                                    //     child: Container(
                                    //         decoration:  BoxDecoration(
                                    //             color: errorMessage != null ? Colors.black.withOpacity(0.65):Colors.black38),
                                    //         child: errorMessage != null ? 
                                    //          Center(child: Text(errorMessage??"",
                                    //          textAlign: TextAlign.center,
                                    //          style: const TextStyle(fontSize: 17,color: Colors.white)))
                                    //         :const Center(child:CircularProgressIndicator()))),
                              //   ],
                              // ),
                             SizedBox(
                            // key: ValueKey(_controller!.value.isFullScreen),
                            height: height10(context) * 25,
                            width: MediaQuery.of(context).size.width,
                            child: PlayAnyVideoPlayer(
                              key: ValueKey(selectedVideo),
                              isLive: true,
                              isCurrentlyOpened: true,
                              // controller: _controller,
                              // onChangedOrientation: (value) {
                              //    isFullscreen = value;
                              //    setState(() { });
                              // },
                              model: Blog(
                                id: liveNews[selectedVideo ?? 0].id,
                                images: [  liveNews[selectedVideo ?? 0].image ],
                                videoUrl: liveNews[selectedVideo ?? 0].url
                              ),
                              videoUrl: liveNews[selectedVideo ?? 0]
                                  .url
                                  .toString(),
                            ),
                          ),
                          
                      selectedVideo == null
                          ? Container()
                          : isFullscreen
                              ? const SizedBox()
                              : Container(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 20, vertical: 16),
                                  child: Row(
                                    children: [
                                      ClipRRect(
                                        borderRadius: BorderRadius.circular(100),
                                        child: CachedNetworkImage(
                                          imageUrl: liveNews[selectedVideo!.toInt()].image.toString(),
                                          height: 30,
                                          width: 30,
                                          fit: BoxFit.cover,
                                        ),
                                      ),
                                      const SizedBox(
                                        width: 10,
                                      ),
                                      Text(
                                        liveNews[selectedVideo!.toInt()]
                                            .companyName
                                            .toString(),
                                        style: Theme.of(context)
                                            .textTheme
                                            .titleLarge!
                                            .copyWith(
                                                fontWeight: FontWeight.w600),
                                      ),
                                    ],
                                  ),
                                ),
                      isFullscreen
                          ? const SizedBox()
                          : const Divider(height: 1, thickness: 1),
                      isFullscreen
                          ? const SizedBox()
                          : Expanded(
                              child: ListView.builder(
                                itemCount: liveNews.length,
                                itemBuilder: (context, index) {
                                  return Container(
                                    key: ValueKey(index),
                                    height: 100,
                                    foregroundDecoration: BoxDecoration(
                                      color: selectedVideo != index
                                          ? Colors.transparent
                                          : Theme.of(context)
                                              .primaryColor
                                              .withOpacity(0.1),
                                    ),
                                    child: LiveWidget(
                                      padding: const EdgeInsets.only(
                                        top: 16,
                                        bottom: 16,
                                      ),
                                      title: liveNews[index].companyName,
                                      fontWeight: FontWeight.w500,
                                      isPlay: selectedVideo == index,
                                      image: liveNews[index].image,
                                      playState: selectedVideo == index,
                                      onShare: () async {
                                        isShare = true;
                                        setState(() {});
                                        await downloadImage(
                                                liveNews[index].image ??
                                                    allSettings.value.appLogo ??
                                                    "")
                                            .then((image) async {
                                          shareImage(image ?? XFile(''),
                                              "${Urls.baseServer}live-news/${liveNews[index].id}");
                                          isShare = false;
                                          setState(() {});
                                        });
                                      },
                                      //&& _controller!.value.isPlaying,
                                      onTap: () async {
                                            
                                            // _controller!.pause();
                                            // _controller = null;
                                            selectedVideo = index;
                                            errorMessage= null;
                                            setState(() { });

                                          //  _controller = await CacheVideoController().playYoutubeVideo(
                                          //   url: liveNews[selectedVideo ?? 0].url ?? "",
                                          //   isLive: true,
                                          //   cacheDuration: const Duration(days: 0));
                                          //   Future.delayed(const Duration(seconds: 8),(){
                                          //     if(_controller == null){
                                          //      errorMessage = "Live Stream ended!";
                                          //       setState(() { });
                                          //     }
                                          //   });
                                          // setVideo();
                                            // setState(() { });
                                      },
                                    ),
                                  );
                                },
                              ),
                            ),
                    ],
                  ),
          
           ) )
        ),
      // ),
    );
  }

  @override
  void dispose() {
    super.dispose();
    // _controller?.dispose();
  }
}
