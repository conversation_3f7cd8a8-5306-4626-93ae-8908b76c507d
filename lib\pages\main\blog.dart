import 'dart:io';
import 'dart:typed_data';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_tts/flutter_tts.dart';
import 'package:html/parser.dart';
import 'package:incite/api_controller/app_provider.dart';
import 'package:incite/api_controller/user_controller.dart';
import 'package:incite/model/blog.dart';
import 'package:incite/pages/main/widgets/share.dart';
import 'package:incite/urls/url.dart';
import 'package:incite/utils/image_util.dart';
import 'package:incite/utils/time_util.dart';
import 'package:incite/widgets/custom_toast.dart';
import 'package:provider/provider.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:just_audio/just_audio.dart' as just;
import 'package:incite/pages/main/web_view.dart';

GlobalKey scaffKey = GlobalKey<ScaffoldState>();

enum TtsState { playing, stopped, paused, continued }

enum BlogOptionType { share, bookmark }

class BlogPage extends StatefulWidget {
  final bool isVoting, isSingle, isBackAllowed;
  final Blog? model;
  final Category? category;
  final BlogOptionType? type;
  final int index, currIndex;
  final VoidCallback? onTap;
  final bool initial;
  final ValueChanged? onChanged;
  final List<GlobalKey>? tutorialkeysList;

  const BlogPage({
    super.key,
    this.model,
    this.type,
    this.onChanged,
    this.isBackAllowed = false,
    this.index = 0,
    this.isSingle = false,
    this.initial = false,
    this.category,
    this.onTap,
    this.tutorialkeysList,
    this.isVoting = false,
    required this.currIndex,
  });

  @override
  State<BlogPage> createState() => _BlogPageState();
}

class _BlogPageState extends State<BlogPage> with WidgetsBindingObserver {
  bool isExpand = false;
  bool isShare = false;
  final audioPlayer = AudioPlayer();
  just.AudioPlayer audioPlay = just.AudioPlayer();
  AppProvider? provider;
  String startTime = '';
  late Map<String, dynamic> ttsData;
  TtsState? ttsState;
  FlutterTts flutterTts = FlutterTts();
  late DateTime blogStartTime;
  bool isVolume = false;
  Uint8List? audioLoad;
  Blog? blog;
  GlobalKey previewContainer = GlobalKey();

  @override
  void initState() {
    super.initState();
    blog = widget.model;
    if (blog != null) {
      WidgetsBinding.instance.addObserver(this);
      blogStartTime = DateTime.now();
      initTTS();
      ttsData = {"id": blog!.id, "start_time": "", "end_time": ""};

      WidgetsBinding.instance.addPostFrameCallback((_) {
        provider = Provider.of<AppProvider>(context, listen: false);
        if (widget.index == 0) {
          provider!.addviewData(blog!.id ?? 0);
        }
        handleInitialAction();
      });
    }
  }

  void handleInitialAction() async {
    if (widget.type == BlogOptionType.share) {
      Future.delayed(const Duration(milliseconds: 1500), () async {
        await captureAndShare();
      });
    } else if (widget.type == BlogOptionType.bookmark) {
      handleBookmark();
    }
  }

  void handleBookmark() {
    if (currentUser.value.id != null) {
      if (!provider!.permanentIds.contains(blog!.id)) {
        showCustomToast(
            context, allMessages.value.bookmarkSave ?? 'Bookmark Saved');
        provider!.addBookmarkData(blog!.id!.toInt());
      } else {
        showCustomToast(
            context, allMessages.value.bookmarkRemove ?? 'Bookmark Removed');
        provider!.removeBookmarkData(blog!.id!.toInt());
      }
      provider!.setBookmark(blog: blog!);
    } else {
      Navigator.pushNamed(context, '/LoginPage');
    }
  }

  Future<void> captureAndShare() async {
    setState(() => isShare = true);
    await Future.delayed(const Duration(milliseconds: 50));
    try {
      final value = await captureScreenshot(previewContainer, isPost: true);
      if (value != null) {
        final data2 = await convertToXFile(value);
        shareImage(data2, "${Urls.baseServer}share-blog?blog_id=${blog!.id}");
        provider!.addShareData(blog!.id!.toInt());
      }
    } catch (e) {
      // Handle error
    } finally {
      if (mounted) {
        setState(() => isShare = false);
      }
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.paused) {
      stops();
    }
    super.didChangeAppLifecycleState(state);
  }

  Future<void> initTTS() async {
    flutterTts = FlutterTts();
    flutterTts.setStartHandler(() => setState(() => ttsState = TtsState.playing));
    flutterTts.setCompletionHandler(() {
      stop();
      var endTime = DateTime.now().toIso8601String();
      ttsData = {
        "id": widget.model!.id,
        "start_time": startTime,
        "end_time": endTime
      };
      provider!.addTtsData(ttsData['id'], ttsData['start_time'], ttsData['end_time']);
    });
    flutterTts.setErrorHandler((msg) => stop());
  }



  Future<void> stop() async {
    await flutterTts.stop();
    setState(() {
      ttsState = TtsState.stopped;
      isVolume = false;
    });
  }

  void stops() {
    if (Platform.isIOS) {
      audioPlay.stop();
    } else {
      audioPlayer.stop();
    }
    setState(() => isVolume = false);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    audioPlayer.dispose();
    audioPlay.dispose();
    flutterTts.stop();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Enhanced error handling for null blog data
    if (blog == null) {
      return Scaffold(
        body: SafeArea(
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 64,
                  color: Colors.grey[400],
                ),
                const SizedBox(height: 16),
                Text(
                  "Article not available",
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: Colors.grey[600],
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  "Please try again later",
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[500],
                  ),
                ),
                const SizedBox(height: 24),
                ElevatedButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text("Go Back"),
                ),
              ],
            ),
          ),
        ),
      );
    }

    // Enhanced validation for blog data
    if (blog!.title == null || blog!.title!.isEmpty) {
      return Scaffold(
        body: SafeArea(
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const CircularProgressIndicator(),
                const SizedBox(height: 16),
                Text(
                  "Loading article...",
                  style: Theme.of(context).textTheme.bodyLarge,
                ),
              ],
            ),
          ),
        ),
      );
    }

    return Scaffold(
      key: scaffKey,
      body: SafeArea(
        child: RepaintBoundary(
          key: previewContainer,
          child: Container(
            color: Theme.of(context).scaffoldBackgroundColor,
            child: Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    child: Column(
                      children: [
                        _buildFeatureImage(),
                        _buildHeadline(),
                        _buildMetaInfo(),
                        _buildArticleBody(),
                        const SizedBox(height: 20),
                        // Banner ad placeholder - implement if needed
                        // if (blog!.description != null && blog!.description!.split(' ').length < 100)
                        //   Center(child: bannerAdMob(context)),
                      ],
                    ),
                  ),
                ),
                _buildFooter(),
                // Share overlay - implement if needed
                // if (isShare)
                //   visibleAtScreenshot(MediaQuery.of(context).orientation, context),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFeatureImage() {
    // Enhanced feature image section with better error handling
    if (blog!.images == null || blog!.images!.isEmpty) {
      // Show placeholder when no image is available
      return Container(
        margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
        height: 220,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.0),
          color: Colors.grey[200],
          border: Border.all(color: Colors.grey[300]!, width: 1),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.image_outlined,
              size: 48,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 8),
            Text(
              "No image available",
              style: TextStyle(
                color: Colors.grey[500],
                fontSize: 14,
              ),
            ),
          ],
        ),
      );
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: Stack(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(12.0),
            child: CachedNetworkImage(
              imageUrl: blog!.images![0],
              placeholder: (context, url) => Container(
                height: 220,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(12.0),
                ),
                child: const Center(
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                  ),
                ),
              ),
              errorWidget: (context, url, error) => Container(
                height: 220,
                decoration: BoxDecoration(
                  color: Colors.grey[200],
                  borderRadius: BorderRadius.circular(12.0),
                  border: Border.all(color: Colors.grey[300]!, width: 1),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Image.asset(
                      Img.logo,
                      height: 60,
                      width: 60,
                      fit: BoxFit.contain,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      "Image not available",
                      style: TextStyle(
                        color: Colors.grey[500],
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
              fit: BoxFit.cover,
              width: double.infinity,
              height: 220,
            ),
          ),
          // Optional: Add source watermark overlay
          if (blog!.sourceName != null && blog!.sourceName!.isNotEmpty)
            Positioned(
              bottom: 8,
              right: 8,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.7),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Text(
                  blog!.sourceName!,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildHeadline() {
    return Container(
      padding: const EdgeInsets.fromLTRB(20, 16, 20, 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Category tag if available
          if (blog!.categoryName != null && blog!.categoryName!.isNotEmpty)
            Container(
              margin: const EdgeInsets.only(bottom: 12),
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: blog!.categoryColor != null
                    ? Color(int.parse(blog!.categoryColor!.replaceFirst('#', '0xFF')))
                    : Theme.of(context).primaryColor,
                borderRadius: BorderRadius.circular(20),
              ),
              child: Text(
                blog!.categoryName!,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          // Main headline
          Text(
            blog!.title ?? 'No Title Available',
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
              fontSize: 24,
              height: 1.3,
              color: Theme.of(context).textTheme.headlineMedium?.color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildArticleBody() {
    String description = parse(blog!.description ?? '').body?.text ?? '';

    // Handle empty description
    if (description.isEmpty) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 16.0),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey[300]!),
          ),
          child: Column(
            children: [
              Icon(
                Icons.article_outlined,
                size: 32,
                color: Colors.grey[400],
              ),
              const SizedBox(height: 8),
              Text(
                "No content available for this article",
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 14,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Enhanced text with better formatting
          RichText(
            text: TextSpan(
              style: TextStyle(
                fontSize: 16,
                height: 1.6,
                color: Theme.of(context).textTheme.bodyMedium!.color,
                fontFamily: Theme.of(context).textTheme.bodyMedium!.fontFamily,
              ),
              children: _buildFormattedText(description),
            ),
            maxLines: isExpand ? null : 5,
            overflow: isExpand ? TextOverflow.visible : TextOverflow.ellipsis,
          ),
          if (description.length > 200)
            Padding(
              padding: const EdgeInsets.only(top: 12.0),
              child: GestureDetector(
                onTap: () => setState(() => isExpand = !isExpand),
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: Theme.of(context).primaryColor.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        isExpand ? 'Read Less' : 'Read More',
                        style: TextStyle(
                          color: Theme.of(context).primaryColor,
                          fontWeight: FontWeight.w600,
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(width: 4),
                      Icon(
                        isExpand ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                        color: Theme.of(context).primaryColor,
                        size: 18,
                      ),
                    ],
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  // Helper method to format text with highlighted keywords
  List<TextSpan> _buildFormattedText(String text) {
    List<TextSpan> spans = [];

    // Keywords to highlight (you can customize this list)
    List<String> keywords = ['breaking', 'urgent', 'important', 'update', 'news', 'alert'];

    List<String> words = text.split(' ');

    for (int i = 0; i < words.length; i++) {
      String word = words[i];
      bool isKeyword = keywords.any((keyword) =>
        word.toLowerCase().contains(keyword.toLowerCase()));

      spans.add(TextSpan(
        text: word + (i < words.length - 1 ? ' ' : ''),
        style: isKeyword ? TextStyle(
          color: Theme.of(context).primaryColor,
          fontWeight: FontWeight.w600,
        ) : null,
      ));
    }

    return spans;
  }

  Widget _buildFooter() {
    provider = Provider.of<AppProvider>(context);
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        border: Border(
          top: BorderSide(color: Colors.grey.shade300, width: 0.5),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _footerButton(
            context,
            provider!.likedIds.contains(blog!.id) ? Icons.favorite : Icons.favorite_border,
            'Like',
            () {
              if (currentUser.value.id != null) {
                provider!.setLike(blog: blog!);
              } else {
                Navigator.pushNamed(context, '/LoginPage');
              }
            },
            color: provider!.likedIds.contains(blog!.id) ? Colors.red : null,
            count: blog!.isVote ?? 0,
          ),
          _footerButton(
            context,
            Icons.comment_outlined,
            'Comment',
            () {
              // TODO: Implement comment functionality
              showCustomToast(context, 'Comments coming soon!');
            },
            count: 0, // TODO: Add comment count from blog model
          ),
          _footerButton(
            context,
            provider!.bookmarkIds.contains(blog!.id) ? Icons.bookmark : Icons.bookmark_border,
            'Save',
            handleBookmark,
            color: provider!.bookmarkIds.contains(blog!.id) ? Theme.of(context).primaryColor : null,
          ),
          _footerButton(
            context,
            Icons.share_outlined,
            'Share',
            captureAndShare,
            isShare: true,
          ),
        ],
      ),
    );
  }

  Widget _footerButton(
    BuildContext context,
    IconData icon,
    String label,
    VoidCallback onPressed, {
    Color? color,
    int? count,
    bool isShare = false,
  }) {
    return Expanded(
      child: InkWell(
        onTap: onPressed,
        borderRadius: BorderRadius.circular(25),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 10),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Stack(
                children: [
                  Icon(
                    icon,
                    color: color ?? Theme.of(context).iconTheme.color?.withValues(alpha: 0.7),
                    size: 24,
                  ),
                  if (isShare)
                    Positioned(
                      right: -2,
                      top: -2,
                      child: Container(
                        width: 8,
                        height: 8,
                        decoration: BoxDecoration(
                          color: Theme.of(context).primaryColor,
                          shape: BoxShape.circle,
                        ),
                      ),
                    ),
                ],
              ),
              const SizedBox(height: 4),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    label,
                    style: TextStyle(
                      fontWeight: FontWeight.w500,
                      fontSize: 12,
                      color: color ?? Theme.of(context).textTheme.bodyMedium?.color?.withValues(alpha: 0.8),
                    ),
                  ),
                  if (count != null && count > 0) ...[
                    const SizedBox(width: 4),
                    Text(
                      count.toString(),
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 12,
                        color: color ?? Theme.of(context).primaryColor,
                      ),
                    ),
                  ],
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMetaInfo() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 12.0),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Row(
                  children: [
                    Icon(
                      Icons.access_time,
                      size: 14,
                      color: Colors.grey.shade600,
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        blog!.createdAt != null
                            ? formatTimeAgo(DateTime.parse(blog!.createdAt!))
                            : 'Unknown time',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade600,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  'Page ${widget.index + 1}',
                  style: TextStyle(
                    fontSize: 11,
                    color: Theme.of(context).primaryColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          if (blog!.sourceLink != null && blog!.sourceLink!.isNotEmpty)
            Padding(
              padding: const EdgeInsets.only(top: 8.0),
              child: Row(
                children: [
                  Icon(
                    Icons.link,
                    size: 14,
                    color: Colors.grey.shade600,
                  ),
                  const SizedBox(width: 4),
                  Expanded(
                    child: GestureDetector(
                      onTap: () {
                        Navigator.push(
                          context,
                          CupertinoPageRoute(
                            builder: (context) => CustomWebView(url: blog!.sourceLink.toString()),
                          ),
                        );
                      },
                      child: Text(
                        blog!.sourceName ?? 'View Source',
                        style: TextStyle(
                          fontSize: 12,
                          color: Theme.of(context).primaryColor,
                          fontWeight: FontWeight.w500,
                          decoration: TextDecoration.underline,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  // Note: timeAndSourceWrap method has been replaced by enhanced _buildMetaInfo method
}
