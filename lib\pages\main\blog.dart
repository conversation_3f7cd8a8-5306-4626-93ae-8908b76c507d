import 'dart:io';
import 'dart:typed_data';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_tts/flutter_tts.dart';
import 'package:html/parser.dart';
import 'package:incite/api_controller/blog_controller.dart';
import 'package:incite/model/analytic.dart';
import 'package:incite/pages/auth/signup.dart';
import 'package:incite/pages/main/web_view.dart';
import 'package:incite/pages/main/widgets/caurosal.dart';
import 'package:incite/pages/main/widgets/fullscreen.dart';
import 'package:incite/pages/main/widgets/poll.dart';
import 'package:incite/pages/main/widgets/share.dart';
import 'package:incite/urls/url.dart';
import 'package:incite/utils/color_util.dart';
import 'package:incite/utils/image_util.dart';
import 'package:incite/utils/nav_util.dart';
import 'package:incite/utils/rgbo_to_hex.dart';
import 'package:incite/utils/screen_util.dart';
import 'package:incite/widgets/incite_video_player.dart';
import 'package:incite/widgets/loader.dart';
import 'package:incite/widgets/shimmer.dart';
import 'package:path_provider/path_provider.dart';
import 'package:provider/provider.dart';
import 'package:visibility_detector/visibility_detector.dart';
import '../../api_controller/app_provider.dart';
import '../../api_controller/user_controller.dart';
import '../../model/blog.dart';
import 'package:audioplayers/audioplayers.dart';
import '../../utils/theme_util.dart';
import '../../utils/time_util.dart';
import '../../utils/tts.dart';
import '../../widgets/banner_ads.dart';
import '../../widgets/custom_toast.dart';
import '../../widgets/tap.dart';
import 'home.dart';
import 'package:just_audio/just_audio.dart' as just;
import 'widgets/text.dart';

class BlogPage extends StatefulWidget {
  final bool isVoting,isSingle,isBackAllowed;
  final Blog? model;
  final Category? category;
  final BlogOptionType? type;
  final int index,currIndex;
  final bool initial;
  final String? type2;
  final Function(bool)? onChanged;
  final VoidCallback? onTap;

  const BlogPage({super.key,
  this.isVoting = false,
  this.isSingle = false,
  this.isBackAllowed = false,
  this.model,
  this.category,
  this.type,
  required this.index,
  required this.currIndex,
  this.initial = false,
  this.type2,
  this.onChanged,
  this.onTap
  });

  @override
  State<BlogPage> createState() => _BlogPageState();
}

// GlobalKey previewContainer2 = GlobalKey();

GlobalKey scaffKey = GlobalKey<ScaffoldState>();

enum TtsState { playing, stopped, paused, continued }

enum BlogOptionType { share, bookmark }

class _BlogPageState extends State<BlogPage> with WidgetsBindingObserver {
  String? vote;
  late Blog blog;
  AppProvider? provider;
  bool isShare = false;
  bool isExpand = false;
  bool isLeftSwipe = false;
  bool isVolume = false;
  GlobalKey previewContainer = GlobalKey();
  GlobalKey previewContainer2 = GlobalKey();
  GlobalKey drawerKey = GlobalKey<ScaffoldState>();

  // TTS related variables
  FlutterTts flutterTts = FlutterTts();
  TtsState ttsState = TtsState.stopped;
  String startTime = '';
  String endTime = '';
  Map<String, dynamic> ttsData = {};
  DateTime blogStartTime = DateTime.now();

  // Audio related variables
  AudioPlayer audioPlayer = AudioPlayer();
  just.AudioPlayer player = just.AudioPlayer();

  // Ad related variables
  int admobFreq = 5;
  int fbadsFreq = 3;

  @override
  void initState() {
    super.initState();
    blog = widget.model!;
    blogStartTime = DateTime.now();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      provider = Provider.of<AppProvider>(context, listen: false);
      if (widget.index == 0) {
        provider!.addviewData(blog.id ?? 0);
      }

      // Initialize TTS
      initTts();
    });

    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    flutterTts.stop();
    audioPlayer.dispose();
    player.dispose();
    super.dispose();
  }

  void initTts() {
    flutterTts.setStartHandler(() {
      setState(() {
        ttsState = TtsState.playing;
      });
    });

    flutterTts.setCompletionHandler(() {
      setState(() {
        ttsState = TtsState.stopped;
      });
    });

    flutterTts.setCancelHandler(() {
      setState(() {
        ttsState = TtsState.stopped;
      });
    });

    flutterTts.setErrorHandler((msg) {
      setState(() {
        ttsState = TtsState.stopped;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
     var  provider2 = Provider.of<AppProvider>(context,listen: false);
    SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle(
      statusBarColor: dark(context) ? ColorUtil.blogBackColor :Colors.white
    ));

    var timeFormat2 = blog.scheduleDate==null ?
     '':timeFormat(DateTime.tryParse(blog.scheduleDate!.toIso8601String()));
    var orientation = MediaQuery.of(context).orientation;
    return SizedBox(
      height: size(context).height,
      width: MediaQuery.of(context).size.width,
      child: CustomLoader(
      isLoading: isShare,
      child: Scaffold(
      key: drawerKey,
      drawerEdgeDragWidth: MediaQuery.of(context).size.width/1.3,
       onEndDrawerChanged: (isOpened) {
        widget.onChanged!(isOpened);
        isLeftSwipe = isOpened;
         setState(() {  });

       },
       endDrawer: blog.sourceLink != ''?
       CustomWebView(url: blog.sourceLink.toString(),
       onTap: () {
        drawerKey.currentState!.closeEndDrawer();
       },
       ): null  ,
       body: blog.title == null && blog.description == null ?
        const Center(child: Text("Blog data is not available."))
        : SafeArea(
         child:Stack(
          fit: StackFit.expand,
                 children: [
                       Container(
                         color: Theme.of(context).scaffoldBackgroundColor,
                         width: size(context).width,
                         height:size(context).height ,
                         child: RepaintBoundary(
                            key: previewContainer2,
                           child: Container(
                            color: Theme.of(context).scaffoldBackgroundColor,
                             child: Column(
                                 children: [
                                  Expanded(
                                 child:RepaintBoundary(
                                  key: previewContainer,
                                  child:  Container(
                                  color: Theme.of(context).scaffoldBackgroundColor,
                                    padding: EdgeInsets.only(
                                     left:  orientation == Orientation.landscape ?0: blog.videoUrl != ''? 0 : 20,
                                     right: orientation == Orientation.landscape ?0: blog.videoUrl != ''? 0 : 20,
                                      top : orientation == Orientation.landscape ? 0 : blog.videoUrl != ''? 0 : height10(context)),
                                    child: Column(
                                     crossAxisAlignment: CrossAxisAlignment.start,
                                     children: [
                                        Column(
                                          children: [
                                              blog.videoUrl!.isNotEmpty  ?
                                               PlayAnyVideoPlayer(
                                                 model: blog,
                                                 isCurrentlyOpened: widget.currIndex == widget.index,
                                                 isPlayCenter: true,
                                              )
                                          : blog.images != null && blog.images!.length > 1 ?
                                           CaurosalSlider(
                                             model: blog,
                                           )  : GestureDetector(
                                           onTap: () {
                                             Navigator.push(context, PagingTransform(
                                               widget: FullScreen(
                                               image:  blog.images != null && blog.images!.isNotEmpty
                                               ? blog.images![0].toString() : '',
                                               index: widget.index,
                                               title: blog.title.toString(),
                                              ),
                                             slideUp: true
                                           ));
                                         },
                                          child: Stack(
                                            children: [
                                              Container(
                                                width: size(context).width,
                                                height: height10(context)*25,
                                                decoration: BoxDecoration(
                                                 color: Theme.of(context).cardColor,
                                                  borderRadius: BorderRadius.circular(20),
                                                ),
                                                child: ClipRRect(
                                                  borderRadius: BorderRadius.circular(20),
                                                  child: blog.images!.isEmpty ?
                                                    Image.asset(Img.logo) :
                                                   Hero(
                                                     tag: blogListHolder.getBlogType() == BlogType.featured ? "Featured${widget.model!.id}" : "${widget.model!.id}",
                                                     child: CachedNetworkImage(
                                                      // imageUrl: '',
                                                      imageUrl: blog.images != null ? blog.images![0] : '',
                                                        fit: BoxFit.cover,
                                                        errorWidget: (context, url, error) {
                                                          return const ShimmerLoader();
                                                        },
                                                        placeholder: (context, url) {
                                                          return const ShimmerLoader();
                                                        },
                                                       ),
                                                   ),
                                                ),
                                               ),
                                               if( blog.videoUrl != '' && widget.currIndex != widget.index )
                                               Positioned.fill(child: Container(
                                                 color: Colors.black54,
                                                 child: const Center(
                                                   child: CircularProgressIndicator(),
                                                 ),
                                               ))
                                            ],
                                          ),
                                        ),
                                         const SizedBox(),
                                        ],
                                       ),
                                     orientation == Orientation.landscape ? const SizedBox():
                                      Padding(
                                      padding: EdgeInsets.only(
                                        left:  orientation == Orientation.landscape ? 0 : blog.videoUrl!.isEmpty ? 0 : 20,
                                        right: orientation == Orientation.landscape ? 0 : blog.videoUrl!.isEmpty ? 0 : 20,
                                      ),
                                        child: TitleWidget(
                                           key:  Key('${ blog.hashCode}'),
                                           title: blog.title.toString()),
                                      ),
                                         // :SizedBox(key: Key('${ widget.index}')) ,
                                        SizedBox(height: height10(context)),
                                        orientation == Orientation.landscape ? const SizedBox() :
                                           Expanded(
                                          flex: 2,
                                           child: Padding(
                                             padding: EdgeInsets.only(
                                                left:  orientation == Orientation.landscape ? 0 : blog.videoUrl!.isEmpty ? 0 : 20,
                                                right: orientation == Orientation.landscape ? 0 : blog.videoUrl!.isEmpty ? 0 : 20,
                                              ),
                                             child: Column(
                                               crossAxisAlignment: CrossAxisAlignment.start,
                                               mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                               children: [
                                                 Expanded(
                                                  child:
                                                    Description(
                                                     optionLength: blog.question == null ? 0:
                                                      blog.question!.options != null ?
                                                      blog.question!.options!.length :0 ,
                                                     model: blog,
                                                     isPoll : isExpand && blog.question != null && blog.isVotingEnable == 1
                                                  ),
                                                  ),
                                               if(blog.question == null && blog.isVotingEnable == 0 )
                                                timeAndSourceWrap(context, timeFormat2),
                                               ],
                                             ),
                                           ),
                                         )
                                     ],
                                    ),
                                  ),
                                ),
                             ),
                                   orientation == Orientation.landscape ? const SizedBox() :
                                    blog.question != null && blog.isVotingEnable == 1 ?
                                        BlogPoll(
                                        pollKey:previewContainer2,
                                        onChanged:(value){
                                           isExpand =value;
                                           setState(() { });
                                        },
                                        model: blog,
                                       ) : const SizedBox(),
                                    if(blog.question != null && blog.isVotingEnable == 1 )
                                     timeAndSourceWrap(context, timeFormat2, isPoll: true),
                                    orientation == Orientation.landscape ? const SizedBox() :
                                    allSettings.value.enableFbAds == '1' && blog.question == null
                                    && widget.index >= int.parse(allSettings.value.fbAdsFrequency.toString()) &&
                                     widget.index % (admobFreq+fbadsFreq) == 0
                                          ?  Platform.isIOS &&  allSettings.value.fbAdsPlacementIdIos!=null
                                          ? facebookads(context)
                                          :Platform.isAndroid &&  allSettings.value.fbAdsPlacementIdAndroid!=null
                                          ? facebookads(context)
                                          : const SizedBox() :
                                          const SizedBox(),

                                          orientation == Orientation.landscape ? const SizedBox() :
                                          allSettings.value.enableAds == '1' && blog.question == null &&
                                          widget.index >= int.parse(allSettings.value.admobFrequency.toString())  &&
                                          widget.index % admobFreq == 0
                                          ? Platform.isIOS &&  allSettings.value.admobBannerIdIos !=null ?
                                           bannerAdMob(context)
                                          :Platform.isAndroid && allSettings.value.admobBannerIdAndroid !=null
                                          ? bannerAdMob(context)
                                          : const SizedBox() : const SizedBox(),
                                 ],
                               ),
                           ),
                         ),
                  ),
                orientation == Orientation.landscape ? const SizedBox() :  Positioned(
                   top: blog.videoUrl != '' ?  height10(context) * 25.5 : height10(context) * 27,
                   // : height10(context) * 24.5,
                    child: Container(
                    width: size(context).width,
                    alignment: Alignment.center,
                    color: Theme.of(context).scaffoldBackgroundColor,
                   padding:  EdgeInsets.only(top: height10(context)-5,bottom: height10(context)-5,left: 24,right: 24),
                   child:  Row(
                   crossAxisAlignment: CrossAxisAlignment.center,
                   mainAxisAlignment: MainAxisAlignment.spaceBetween,
                   children: [
                   Row(
                     children : blog.blogSubCategory != null && blog.blogSubCategory!.isNotEmpty ? [
                      ...blog.blogSubCategory!.take(1).map((e) =>
                      CategoryWrap(color: e.color != null ? hexToRgb(e.color.toString()) : Colors.black,
                         name: e.name.toString()),)
                     ] : [
                        CategoryWrap(color: blog.categoryColor != null ? hexToRgb(blog.categoryColor.toString()) : Colors.black,
                         name: blog.categoryName.toString()),
                     ],
                   ),
                     VisibilityDetector(
                      key: Key(blog.title.toString()),
                      onVisibilityChanged :
                           (visibilityInfo) async {
                         // print(isPlay);
                         var visiblePercentage =
                             visibilityInfo.visibleFraction *
                                 100.0;
                          // if (visiblePercentage == 100.0) {
                          //    provider!.addviewData(blog.id!.toInt());
                          // }
                         if (visiblePercentage != 100.0) {
                           var blogendTime = DateTime.now();
                           provider!.addBlogTimeSpent(BlogTime(
                                    id: blog.id,
                                    startTime:blogStartTime,
                                    endTime:blogendTime,
                                    timeSpent:blogendTime.difference(blogStartTime).inSeconds
                                  ));
                         }
                         if (visiblePercentage == 100.0) {
                           provider!.addviewData(blog.id!.toInt());
                         }
                         if (visiblePercentage == 0.0) {
                           if (isVolume) {
                             flutterTts.stop();
                             isVolume = false;
                             setState(() {     });
                           }
                         }
                       },
                       child:  PostFeatureWrap(
                             isVolume: isVolume,
                             onVoice:() async{
                               if(allSettings.value.googleApikey != null && allSettings.value.isVoiceEnabled==true)  {
                                 if(isVolume){
                                   flutterTts.stop();
                                   isVolume = false;
                                   setState(() {  });
                                 }else{
                                   isVolume = true;
                                   setState(() {  });
                                   await flutterTts.speak(blog.title.toString() + blog.description.toString());
                                 }
                               }else{
                                 showCustomToast(context, 'Voice feature is not enabled');
                               }
                             },
                             onBookmark: () {
                               if(currentUser.value.id != null){
                                 if(provider!.bookmarkIds.contains(blog.id)){
                                   showCustomToast(context,allMessages.value.bookmarkRemove ??'Bookmark Removed');
                                   provider!.removeBookmarkData(blog.id!.toInt());
                                 }
                                 provider!.setBookmark(blog:blog as Blog);
                                 setState(() {  });

                               }else{
                                 Navigator.pushNamed(context, '/LoginPage');
                               }
                             },
                             onLike: () {
                               if(currentUser.value.id != null){
                                 provider!.setLike(blog:blog);
                               }else{
                                 Navigator.pushNamed(context, '/LoginPage');
                               }
                             },
                             onShare: captureAndShare,
                             onComment: () {
                               showCustomToast(context, 'Comments coming soon!');
                             },
                             model: blog,
                           ),
                     )
                  ],
                 ),
               ),
              ),
                 ],
               ),
             ),
           ),
         ),
       ),
     );
  }

  void handleBookmark() {
    if(currentUser.value.id != null){
      if(provider!.bookmarkIds.contains(blog.id)){
        showCustomToast(context,allMessages.value.bookmarkRemove ??'Bookmark Removed');
        provider!.removeBookmarkData(blog.id!.toInt());
      }
      provider!.setBookmark(blog:blog as Blog);
      setState(() {  });

    }else{
      Navigator.pushNamed(context, '/LoginPage');
    }
  }

  Container timeAndSourceWrap(BuildContext context, String timeFormat2, {bool isPoll = false}) {
    return Container(
      alignment: Alignment.centerLeft,
      padding: EdgeInsets.only(
        left: isPoll ? 18 : 0,
        top: 8,
        bottom: isPoll ? 12 : 6
      ),
      child: RichText(
        text: TextSpan(
          children: [
            if (blog.sourceLink != '' && blog.sourceLink != null)
              WidgetSpan(
                alignment: PlaceholderAlignment.middle,
                child: TapInk(
                  onTap: () {
                    Navigator.push(
                      context,
                      CupertinoPageRoute(
                        builder: (context) => CustomWebView(url: blog.sourceLink.toString())
                      )
                    );
                  },
                  child: Container(
                    decoration: BoxDecoration(
                      border: Border(
                        bottom: BorderSide(
                          width: 1,
                          color: isBlack(Theme.of(context).primaryColor) && dark(context)
                              ? Colors.white
                              : Theme.of(context).primaryColor
                        )
                      )
                    ),
                    child: Text(
                      '${blog.sourceName}',
                      style: TextStyle(
                        fontFamily: 'Roboto',
                        fontSize: 14,
                        fontWeight: FontWeight.w300,
                        color: isBlack(Theme.of(context).primaryColor) && dark(context)
                            ? Colors.white
                            : Theme.of(context).primaryColor,
                      )
                    ),
                  ),
                )
              ),
            TextSpan(
              text: blog.sourceLink == ''
                  ? timeFormat2
                  : " : $timeFormat2",
              style: TextStyle(
                fontFamily: 'Roboto',
                fontSize: 14,
                fontWeight: FontWeight.w300,
                color: dark(context) ? ColorUtil.white : ColorUtil.textgrey,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget facebookads(BuildContext context) {
    return Container(
  width: size(context).width,
  height: height10(context)*5,
  alignment: Alignment.center,
  decoration: BoxDecoration(
    borderRadius: BorderRadius.circular(10),
  ),
  child:  FacebookAd(
          adUnitId :Platform.isIOS
            ? allSettings.value.fbAdsPlacementIdIos?? ''
            :  allSettings.value.fbAdsPlacementIdAndroid ?? '' ,
        ));
  }

  Widget bannerAdMob(BuildContext context) {
    return Container(
      width: size(context).width,
      height: height10(context)*5,
      alignment: Alignment.center,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
      ),
      child: BannerAdMob(
        adUnitId: Platform.isIOS
            ? allSettings.value.admobBannerIdIos ?? ''
            : allSettings.value.admobBannerIdAndroid ?? '',
      ),
    );
  }

  // Capture and share functionality
  Future<void> captureAndShare() async {
    isShare = true;
    setState(() {});

    Future.delayed(const Duration(milliseconds: 100));
    await captureScreenshot(previewContainer, isPost: true).then((value) async {
      Future.delayed(const Duration(milliseconds: 10));
      final data2 = await convertToXFile(value!);
      Future.delayed(const Duration(milliseconds: 10));
      shareImage(data2, "${Urls.baseServer}blog-share?id=${blog.id}");
      provider!.addShareData(blog.id!.toInt());
      isShare = false;
      setState(() {});
    });
  }

  // Loading state with shimmer
  Widget _buildLoadingState() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          ShimmerLoader(
            width: size(context).width,
            height: 250,
            margin: const EdgeInsets.only(top: 30),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              const ShimmerLoader(
                width: 100,
                height: 20,
                borderRadius: 16,
              ),
              const Spacer(),
              ...List.generate(
                3,
                (index) => const ShimmerLoader(
                  width: 36,
                  height: 36,
                  borderRadius: 100,
                ),
              )
            ],
          ),
          const SizedBox(height: 12),
          ShimmerLoader(
            width: size(context).width,
            height: size(context).height / 2.15,
            borderRadius: 16,
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              ...List.generate(
                2,
                (index) => const ShimmerLoader(
                  width: 120,
                  height: 20,
                  borderRadius: 10,
                ),
              )
            ],
          )
        ],
      ),
    );
  }

  // 1. Feature Image Section - Large Banner Image at the top (full width)
  Widget _buildFeatureImageSection() {
    return Container(
      width: double.infinity,
      height: 250,
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12), // Slightly rounded corners
        color: Theme.of(context).cardColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Stack(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: _buildImageContent(),
          ),
          // Source watermark overlay (small source tag)
          if (blog.sourceName != null && blog.sourceName!.isNotEmpty)
            Positioned(
              bottom: 8,
              right: 8,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.7),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Text(
                  blog.sourceName!,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
          // Category tag overlay
          Positioned(
            top: 12,
            left: 12,
            child: _buildCategoryTag(),
          ),
        ],
      ),
    );
  }

  Widget _buildImageContent() {
    if (blog.videoUrl != null && blog.videoUrl!.isNotEmpty) {
      return PlayAnyVideoPlayer(
        model: blog,
        isCurrentlyOpened: widget.currIndex == widget.index,
        isPlayCenter: true,
      );
    } else if (blog.images != null && blog.images!.length > 1) {
      return CaurosalSlider(model: blog);
    } else {
      return GestureDetector(
        onTap: () {
          Navigator.push(
            context,
            PagingTransform(
              widget: FullScreen(
                image: blog.images != null && blog.images!.isNotEmpty
                    ? blog.images![0].toString()
                    : '',
                index: widget.index,
                title: blog.title.toString(),
              ),
              slideUp: true,
            ),
          );
        },
        child: blog.images == null || blog.images!.isEmpty
            ? Image.asset(Img.logo, fit: BoxFit.cover)
            : CachedNetworkImage(
                imageUrl: blog.images![0],
                fit: BoxFit.cover,
                width: double.infinity,
                height: double.infinity,
                errorWidget: (context, url, error) => const ShimmerLoader(),
                placeholder: (context, url) => const ShimmerLoader(),
              ),
      );
    }
  }

  Widget _buildCategoryTag() {
    if (blog.blogSubCategory != null && blog.blogSubCategory!.isNotEmpty) {
      return CategoryWrap(
        color: blog.blogSubCategory![0].color != null
            ? hexToRgb(blog.blogSubCategory![0].color.toString())
            : Colors.black,
        name: blog.blogSubCategory![0].name.toString(),
      );
    } else {
      return CategoryWrap(
        color: blog.categoryColor != null
            ? hexToRgb(blog.categoryColor.toString())
            : Colors.black,
        name: blog.categoryName.toString(),
      );
    }
  }

  // 2. Headline/Title Section - Bold and large font, center-aligned
  Widget _buildHeadlineSection() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
      child: TitleWidget(
        key: Key('${blog.hashCode}'),
        title: blog.title.toString(),
        size: 24, // Large font size for high visibility
        color: Theme.of(context).textTheme.headlineLarge?.color,
      ),
    );
  }

  // 5. Meta Info Row - Time posted and page indicator (small, faded font)
  Widget _buildMetaInfoSection(String timeFormat2) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Time and source info
          Expanded(
            child: _buildTimeAndSourceInfo(timeFormat2),
          ),
          // Page indicator
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              '${widget.index + 1} of 50', // Page indicator
              style: TextStyle(
                fontSize: 11,
                color: Theme.of(context).textTheme.bodySmall?.color?.withOpacity(0.7),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTimeAndSourceInfo(String timeFormat2) {
    return RichText(
      text: TextSpan(
        children: [
          if (blog.sourceLink != null && blog.sourceLink!.isNotEmpty)
            WidgetSpan(
              alignment: PlaceholderAlignment.middle,
              child: GestureDetector(
                onTap: () {
                  Navigator.push(
                    context,
                    CupertinoPageRoute(
                      builder: (context) => CustomWebView(url: blog.sourceLink.toString()),
                    ),
                  );
                },
                child: Container(
                  decoration: BoxDecoration(
                    border: Border(
                      bottom: BorderSide(
                        width: 1,
                        color: Theme.of(context).primaryColor,
                      ),
                    ),
                  ),
                  child: Text(
                    blog.sourceName ?? 'Source',
                    style: TextStyle(
                      color: Theme.of(context).primaryColor,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
            ),
          if (blog.sourceLink != null && blog.sourceLink!.isNotEmpty && timeFormat2.isNotEmpty)
            const TextSpan(text: ' • '),
          if (timeFormat2.isNotEmpty)
            TextSpan(
              text: timeFormat2,
              style: TextStyle(
                color: Theme.of(context).textTheme.bodySmall?.color?.withOpacity(0.7),
                fontSize: 12,
              ),
            ),
        ],
      ),
    );
  }

  // 3. Article Body/Summary Section - Clean text with scrolling
  Widget _buildArticleBodySection() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Description(
            optionLength: blog.question == null
                ? 0
                : blog.question!.options != null
                    ? blog.question!.options!.length
                    : 0,
            model: blog,
            isPoll: isExpand && blog.question != null && blog.isVotingEnable == 1,
          ),
          if (blog.question != null && blog.isVotingEnable == 1) ...[
            const SizedBox(height: 16),
            BlogPoll(
              pollKey: previewContainer,
              onChanged: (value) {
                isExpand = value;
                setState(() {});
              },
              model: blog,
            ),
          ],
        ],
      ),
    );
  }

  // Ad Section
  Widget _buildAdSection() {
    return const SizedBox(); // Placeholder for ads
  }

  // 4. Footer/Engagement Bar - Fixed at bottom with like, comment, share
  Widget _buildFooterEngagementBar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        border: Border(
          top: BorderSide(color: Colors.grey.shade300, width: 0.5),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          // Like button
          _buildEngagementButton(
            icon: provider != null && provider!.likedIds.contains(blog.id)
                ? Icons.favorite
                : Icons.favorite_border,
            label: 'Like',
            count: blog.isVote ?? 0,
            color: provider != null && provider!.likedIds.contains(blog.id)
                ? Colors.red
                : null,
            onTap: _handleLike,
          ),

          // Comment button
          _buildEngagementButton(
            icon: Icons.comment_outlined,
            label: 'Comment',
            count: 0, // Add comment count when available
            onTap: () {
              showCustomToast(context, 'Comments coming soon!');
            },
          ),

          // Bookmark button
          _buildEngagementButton(
            icon: provider != null && provider!.bookmarkIds.contains(blog.id)
                ? Icons.bookmark
                : Icons.bookmark_border,
            label: 'Save',
            color: provider != null && provider!.bookmarkIds.contains(blog.id)
                ? Theme.of(context).primaryColor
                : null,
            onTap: _handleBookmark,
          ),

          // Share button (right-aligned with arrow)
          _buildEngagementButton(
            icon: Icons.share_outlined,
            label: 'Share',
            onTap: _handleShare,
            isShare: true,
          ),
        ],
      ),
    );
  }

  // Helper method to build engagement buttons
  Widget _buildEngagementButton({
    required IconData icon,
    required String label,
    int? count,
    Color? color,
    required VoidCallback onTap,
    bool isShare = false,
  }) {
    return Expanded(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(25),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 10),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Stack(
                children: [
                  Icon(
                    icon,
                    color: color ?? Theme.of(context).iconTheme.color?.withOpacity(0.7),
                    size: 24,
                  ),
                  if (isShare)
                    Positioned(
                      right: -2,
                      top: -2,
                      child: Container(
                        width: 8,
                        height: 8,
                        decoration: BoxDecoration(
                          color: Theme.of(context).primaryColor,
                          shape: BoxShape.circle,
                        ),
                      ),
                    ),
                ],
              ),
              const SizedBox(height: 4),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    label,
                    style: TextStyle(
                      fontWeight: FontWeight.w500,
                      fontSize: 12,
                      color: color ?? Theme.of(context).textTheme.bodyMedium?.color?.withOpacity(0.8),
                    ),
                  ),
                  if (count != null && count > 0) ...[
                    const SizedBox(width: 4),
                    Text(
                      count.toString(),
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 12,
                        color: color ?? Theme.of(context).primaryColor,
                      ),
                    ),
                  ],
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Handle like action
  void _handleLike() {
    if (currentUser.value.id != null) {
      provider?.setLike(blog: blog);
      setState(() {});
    } else {
      Navigator.pushNamed(context, '/LoginPage');
    }
  }

  // Handle bookmark action
  void _handleBookmark() {
    if (currentUser.value.id != null) {
      if (provider != null && provider!.bookmarkIds.contains(blog.id)) {
        showCustomToast(context, 'Bookmark Removed');
        provider!.removeBookmarkData(blog.id!.toInt());
      } else {
        showCustomToast(context, 'Bookmark Saved');
        provider!.addBookmarkData(blog.id!.toInt());
      }
      provider?.setBookmark(blog: blog);
      setState(() {});
    } else {
      Navigator.pushNamed(context, '/LoginPage');
    }
  }

  // Handle share action
  void _handleShare() {
    showCustomToast(context, 'Share functionality coming soon!');
    // TODO: Implement share functionality
  }
}
