import 'dart:io';
import 'dart:typed_data';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_tts/flutter_tts.dart';
import 'package:html/parser.dart';
import 'package:incite/api_controller/app_provider.dart';
import 'package:incite/api_controller/user_controller.dart';
import 'package:incite/model/blog.dart';
import 'package:incite/pages/main/widgets/share.dart';
import 'package:incite/urls/url.dart';
import 'package:incite/utils/color_util.dart';
import 'package:incite/utils/image_util.dart';
import 'package:incite/utils/theme_util.dart';
import 'package:incite/utils/time_util.dart';
import 'package:incite/widgets/banner_ads.dart';
import 'package:incite/widgets/custom_toast.dart';
import 'package:provider/provider.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:just_audio/just_audio.dart' as just;
import 'package:path_provider/path_provider.dart';
import 'package:incite/utils/tts.dart';
import 'package:incite/pages/main/web_view.dart';
import 'package:incite/widgets/tap.dart';
import 'package:incite/utils/screen_util.dart';
import 'home.dart';

GlobalKey scaffKey = GlobalKey<ScaffoldState>();

enum TtsState { playing, stopped, paused, continued }

enum BlogOptionType { share, bookmark }

class BlogPage extends StatefulWidget {
  final bool isVoting, isSingle, isBackAllowed;
  final Blog? model;
  final Category? category;
  final BlogOptionType? type;
  final int index, currIndex;
  final VoidCallback? onTap;
  final bool initial;
  final ValueChanged? onChanged;
  final List<GlobalKey>? tutorialkeysList;

  const BlogPage({
    super.key,
    this.model,
    this.type,
    this.onChanged,
    this.isBackAllowed = false,
    this.index = 0,
    this.isSingle = false,
    this.initial = false,
    this.category,
    this.onTap,
    this.tutorialkeysList,
    this.isVoting = false,
    required this.currIndex,
  });

  @override
  State<BlogPage> createState() => _BlogPageState();
}

class _BlogPageState extends State<BlogPage> with WidgetsBindingObserver {
  bool isExpand = false;
  bool isShare = false;
  final audioPlayer = AudioPlayer();
  just.AudioPlayer audioPlay = just.AudioPlayer();
  AppProvider? provider;
  String startTime = '';
  late Map<String, dynamic> ttsData;
  var ttsState;
  FlutterTts flutterTts = FlutterTts();
  late DateTime blogStartTime;
  bool isVolume = false;
  Uint8List? audioLoad;
  Blog? blog;
  GlobalKey previewContainer = GlobalKey();

  @override
  void initState() {
    super.initState();
    blog = widget.model;
    if (blog != null) {
      WidgetsBinding.instance.addObserver(this);
      blogStartTime = DateTime.now();
      initTTS();
      ttsData = {"id": blog!.id, "start_time": "", "end_time": ""};

      WidgetsBinding.instance.addPostFrameCallback((_) {
        provider = Provider.of<AppProvider>(context, listen: false);
        if (widget.index == 0) {
          provider!.addviewData(blog!.id ?? 0);
        }
        handleInitialAction();
      });
    }
  }

  void handleInitialAction() async {
    if (widget.type == BlogOptionType.share) {
      Future.delayed(const Duration(milliseconds: 1500), () async {
        await captureAndShare();
      });
    } else if (widget.type == BlogOptionType.bookmark) {
      handleBookmark();
    }
  }

  void handleBookmark() {
    if (currentUser.value.id != null) {
      if (!provider!.permanentIds.contains(blog!.id)) {
        showCustomToast(
            context, allMessages.value.bookmarkSave ?? 'Bookmark Saved');
        provider!.addBookmarkData(blog!.id!.toInt());
      } else {
        showCustomToast(
            context, allMessages.value.bookmarkRemove ?? 'Bookmark Removed');
        provider!.removeBookmarkData(blog!.id!.toInt());
      }
      provider!.setBookmark(blog: blog!);
    } else {
      Navigator.pushNamed(context, '/LoginPage');
    }
  }

  Future<void> captureAndShare() async {
    setState(() => isShare = true);
    await Future.delayed(const Duration(milliseconds: 50));
    try {
      final value = await captureScreenshot(previewContainer, isPost: true);
      if (value != null) {
        final data2 = await convertToXFile(value);
        shareImage(data2, "${Urls.baseServer}share-blog?blog_id=${blog!.id}");
        provider!.addShareData(blog!.id!.toInt());
      }
    } catch (e) {
      // Handle error
    } finally {
      if (mounted) {
        setState(() => isShare = false);
      }
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.paused) {
      stops();
    }
    super.didChangeAppLifecycleState(state);
  }

  Future<void> initTTS() async {
    flutterTts = FlutterTts();
    flutterTts.setStartHandler(() => setState(() => ttsState = TtsState.playing));
    flutterTts.setCompletionHandler(() {
      stop();
      var endTime = DateTime.now().toIso8601String();
      ttsData = {
        "id": widget.model!.id,
        "start_time": startTime,
        "end_time": endTime
      };
      provider!.addTtsData(ttsData['id'], ttsData['start_time'], ttsData['end_time']);
    });
    flutterTts.setErrorHandler((msg) => stop());
  }

  Future<void> _playVoice(String text) async {
    setState(() => isVolume = true);
    await flutterTts.speak(text);
  }

  Future<void> stop() async {
    await flutterTts.stop();
    setState(() {
      ttsState = TtsState.stopped;
      isVolume = false;
    });
  }

  void stops() {
    if (Platform.isIOS) {
      audioPlay.stop();
    } else {
      audioPlayer.stop();
    }
    setState(() => isVolume = false);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    audioPlayer.dispose();
    audioPlay.dispose();
    flutterTts.stop();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (blog == null) {
      return const Scaffold(
        body: Center(child: Text("Blog data is not available.")),
      );
    }

    return Scaffold(
      key: scaffKey,
      body: SafeArea(
        child: RepaintBoundary(
          key: previewContainer,
          child: Container(
            color: Theme.of(context).scaffoldBackgroundColor,
            child: Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    child: Column(
                      children: [
                        _buildFeatureImage(),
                        _buildHeadline(),
                        _buildMetaInfo(),
                        _buildArticleBody(),
                        const SizedBox(height: 20),
                        if (blog!.description != null && blog!.description!.split(' ').length < 100)
                          Center(child: bannerAdMob(context)),
                      ],
                    ),
                  ),
                ),
                _buildFooter(),
                if (isShare)
                  visibleAtScreenshot(MediaQuery.of(context).orientation, context),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFeatureImage() {
    if (blog!.images == null || blog!.images!.isEmpty) {
      return const SizedBox.shrink();
    }
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12.0),
        child: CachedNetworkImage(
          imageUrl: blog!.images![0],
          placeholder: (context, url) => Container(
            height: 200,
            color: Colors.grey[300],
            child: const Center(child: CircularProgressIndicator()),
          ),
          errorWidget: (context, url, error) => Image.asset(Img.logo, fit: BoxFit.cover, height: 200),
          fit: BoxFit.cover,
          width: double.infinity,
          height: 220,
        ),
      ),
    );
  }

  Widget _buildHeadline() {
    return Padding(
      padding: const EdgeInsets.fromLTRB(20, 16, 20, 8),
      child: Text(
        blog!.title ?? 'No Title',
        textAlign: TextAlign.center,
        style: const TextStyle(
          fontWeight: FontWeight.bold,
          fontSize: 24,
          height: 1.3,
        ),
      ),
    );
  }

  Widget _buildArticleBody() {
    String description = parse(blog!.description ?? '').body?.text ?? '';
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            description,
            maxLines: isExpand ? 1000 : 5,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
              fontSize: 16,
              height: 1.6,
              color: Theme.of(context).textTheme.bodyMedium!.color!.withOpacity(0.8),
            ),
          ),
          if (description.length > 200)
            Padding(
              padding: const EdgeInsets.only(top: 8.0),
              child: GestureDetector(
                onTap: () => setState(() => isExpand = !isExpand),
                child: Text(
                  isExpand ? 'Read Less' : 'Read More',
                  style: TextStyle(
                    color: Theme.of(context).primaryColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildFooter() {
    provider = Provider.of<AppProvider>(context);
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(color: Colors.grey.shade300, width: 1),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _footerButton(
            context,
            provider!.likedIds.contains(blog!.id) ? Icons.favorite : Icons.favorite_border,
            'Like',
            () {
              if (currentUser.value.id != null) {
                provider!.setLike(blog: blog!);
              } else {
                Navigator.pushNamed(context, '/LoginPage');
              }
            },
            color: provider!.likedIds.contains(blog!.id) ? Colors.red : null,
          ),
          _footerButton(
            context,
            provider!.bookmarkIds.contains(blog!.id) ? Icons.bookmark : Icons.bookmark_border,
            'Save',
            handleBookmark,
            color: provider!.bookmarkIds.contains(blog!.id) ? Theme.of(context).primaryColor : null,
          ),
          _footerButton(context, Icons.share_outlined, 'Share', captureAndShare),
        ],
      ),
    );
  }

  Widget _footerButton(BuildContext context, IconData icon, String label, VoidCallback onPressed, {Color? color}) {
    return InkWell(
      onTap: onPressed,
      borderRadius: BorderRadius.circular(20),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        child: Row(
          children: [
            Icon(icon, color: color ?? Theme.of(context).iconTheme.color, size: 22),
            const SizedBox(width: 6),
            Text(label, style: TextStyle(fontWeight: FontWeight.w500)),
          ],
        ),
      ),
    );
  }

  Widget _buildMetaInfo() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: timeAndSourceWrap(context, formatTimeAgo(DateTime.parse(blog!.createdAt!))),
          ),
          Text(
            'Page ${widget.index + 1} of 50', // Example page number
            style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
          ),
        ],
      ),
    );
  }

  Container timeAndSourceWrap(BuildContext context, String timeFormat2, {bool isPoll = false}) {
    return Container(
      alignment: Alignment.centerLeft,
      child: RichText(
        text: TextSpan(
          children: [
            if (blog!.sourceLink != null && blog!.sourceLink!.isNotEmpty)
              WidgetSpan(
                alignment: PlaceholderAlignment.middle,
                child: TapInk(
                  onTap: () {
                    Navigator.push(
                      context,
                      CupertinoPageRoute(
                        builder: (context) => CustomWebView(url: blog!.sourceLink.toString()),
                      ),
                    );
                  },
                  child: Container(
                    decoration: BoxDecoration(
                      border: Border(
                        bottom: BorderSide(
                          width: 1,
                          color: isBlack(Theme.of(context).primaryColor) && dark(context)
                              ? Colors.white
